using Sansar;
using Sansar.Script;
using Sansar.Simulation;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Diagnostics;


/// <summary>
/// Unified Tetris Game Script - Single script combining all game functionality
/// Place this script on the Tetris seat object in your scene
/// </summary>
public class TetrisGame : SceneObjectScript
{
    #region Editor Properties

    // Grid and Block Configuration
    [DisplayName("Tetris Block")]
    [Tooltip("Generic block to spawn for each grid position")]
    public ClusterResource GenericBlock;
    
    [DisplayName("Background Block")]
    [Tooltip("Background block model for grid reference")]
    public ClusterResource BackgroundBlock;
    
    [DisplayName("Ghost Block")]
    [Tooltip("Ghost block model with alpha transparency support")]
    public ClusterResource GhostBlock;
    
    [Tooltip("Chair/seat object for additional player boards")]
    public ClusterResource ChairResource;
    
    // Multi-Material Configuration
    [DisplayName("Enable Multi-Mat")]
    [DefaultValue(true)]
    [Tooltip("Enable multi-material system for enhanced visual control")]
    public bool EnableMultiMaterial = true;
    
    [DisplayName("BgCube Base Color")]
    [DefaultValue(0.0f, 0.0f, 0.0f, 1.0f)]
    [Tooltip("Color for background block base material (default: black)")]
    public Sansar.Color BackgroundBaseColor = Sansar.Color.Black;
    
    [DisplayName("BgCube Highlight Color")]
    [DefaultValue(0.2f, 0.2f, 0.2f, 1.0f)]
    [Tooltip("Color for background block highlight material")]
    public Sansar.Color BackgroundHighlightColor = new Sansar.Color(0.2f, 0.2f, 0.2f, 1.0f);
    
    [DisplayName("BgCube Highlight Emissive")]
    [DefaultValue(0.1f)]
    [Range(0.0f, 5.0f)]
    [Tooltip("Emissive intensity for background highlight material")]
    public float BackgroundHighlightEmissive = 0.1f;
    
    [Tooltip("Grid origin point in world space")]
    public Vector GridOrigin;
    
    [Tooltip("Chair origin point in world space - position for the first chair (Board 0)")]
    public Vector ChairOrigin;
    
    [Tooltip("Spawn blocks in batches to prevent throttling")]
    [DefaultValue(10)]
    public int BatchSize = 10;
    
    [Tooltip("Delay between batches in seconds")]
    [DefaultValue(0.1f)]
    public float BatchDelay = 0.1f;
    
    [Tooltip("Number of game boards to create at startup")]
    [DefaultValue(1)]
    [Range(1, 8)]
    public int DefaultBoardCount = 1;
    
    [Tooltip("Enable rainbow debug mode for testing material control")]
    [DefaultValue(false)]
    public bool DebugRainbowMode = false;
    
    [DisplayName("Enable Ghost Piece")]
    [DefaultValue(true)]
    [Tooltip("Show ghost preview of where piece will land when hard dropped")]
    public bool EnableGhostPiece = true;
    
    // Audio Configuration
    [DisplayName("Move Sound")]
    [Tooltip("Sound played when piece moves left/right/down")]
    public SoundResource MoveSound;
    
    [DisplayName("Move Volume")]
    [Range(0.0f, 100.0f)]
    [DefaultValue(40.0f)]
    [Tooltip("Volume for movement sounds (0-100%)")]
    public float MoveVolume = 80.0f;
    
    [DisplayName("Rotate Sound")]
    [Tooltip("Sound played when piece rotates")]
    public SoundResource RotateSound;
    
    [DisplayName("Rotate Volume")]
    [Range(0.0f, 100.0f)]
    [DefaultValue(40.0f)]
    [Tooltip("Volume for rotation sounds (0-100%)")]
    public float RotateVolume = 80.0f;
    
    [DisplayName("Hard Drop Sound")]
    [Tooltip("Sound played when piece is hard dropped")]
    public SoundResource HardDropSound;
    
    [DisplayName("Hard Drop Volume")]
    [Range(0.0f, 100.0f)]
    [DefaultValue(60.0f)]
    [Tooltip("Volume for hard drop sounds (0-100%)")]
    public float HardDropVolume = 80.0f;
    
    [DisplayName("Line Clear Sound")]
    [Tooltip("Sound played when 1-3 lines are cleared")]
    public SoundResource LineClearSound;
    
    [DisplayName("Line Clear Volume")]
    [Range(0.0f, 100.0f)]
    [DefaultValue(50.0f)]
    [Tooltip("Volume for line clear sounds (0-100%)")]
    public float LineClearVolume = 60.0f;
    
    [DisplayName("Tetris Sound")]
    [Tooltip("Special sound played when 4 lines are cleared (Tetris)")]
    public SoundResource TetrisSound;
    
    [DisplayName("Tetris Volume")]
    [Range(0.0f, 100.0f)]
    [DefaultValue(70.0f)]
    [Tooltip("Volume for Tetris sounds (0-100%)")]
    public float TetrisVolume = 50.0f;
    
    [DisplayName("Lock Piece Sound")]
    [Tooltip("Sound played when piece locks into place")]
    public SoundResource LockPieceSound;
    
    [DisplayName("Lock Piece Volume")]
    [Range(0.0f, 100.0f)]
    [DefaultValue(30.0f)]
    [Tooltip("Volume for lock piece sounds (0-100%)")]
    public float LockPieceVolume = 80.0f;
    
    [DisplayName("Game Over Sound")]
    [Tooltip("Sound played when the game ends")]
    public SoundResource GameOverSound;
    
    [DisplayName("Game Over Volume")]
    [Range(0.0f, 100.0f)]
    [DefaultValue(60.0f)]
    [Tooltip("Volume for game over sound (0-100%)")]
    public float GameOverVolume = 80.0f;
    
    // Leaderboard Configuration
    [DisplayName("PS ID")]
    [Tooltip("Unique identifier for the leaderboard data store. Use same ID across scenes to share leaderboards, or different IDs for separate leaderboards.")]
    [DefaultValue("TetrisLeaderboard_V1_UniqueKey2025")]
    public string LeaderboardStoreId = "TetrisLeaderboard_V1_UniqueKey2025";
    
    // Background Music Configuration
    [DisplayName("Bg Track 1")]
    [Tooltip("Background music track 1")]
    public SoundResource BackgroundMusic1;
    
    [DisplayName("Bg Track 2")]
    [Tooltip("Background music track 2")]
    public SoundResource BackgroundMusic2;
    
    [DisplayName("Bg Track 3")]
    [Tooltip("Background music track 3")]
    public SoundResource BackgroundMusic3;
    
    [DisplayName("Bg Track 4")]
    [Tooltip("Background music track 4")]
    public SoundResource BackgroundMusic4;

    [DisplayName("Bg Track 5")]
    [Tooltip("Background music track 5")]
    public SoundResource BackgroundMusic5;

    [DisplayName("Bg Track 6")]
    [Tooltip("Background music track 6")]
    public SoundResource BackgroundMusic6;

    [DisplayName("Background Music Volume")]
    [Range(0.0f, 100.0f)]
    [DefaultValue(30.0f)]
    [Tooltip("Default volume for background music (0-100%)")]
    public float BackgroundMusicVolume = 30.0f;
    
    [DisplayName("Background Music Enabled")]
    [DefaultValue(true)]
    [Tooltip("Start with background music playing")]
    public bool BackgroundMusicEnabled = true;
        
    #endregion

    #region Game Constants and Enums

    public const int GRID_WIDTH = 10;
    public const int GRID_HEIGHT = 20;
    public const float BLOCK_SIZE = 1.0f;
    
    // Next piece preview area
    public const int PREVIEW_WIDTH = 4;
    public const int PREVIEW_HEIGHT = 4;
    public const int PREVIEW_OFFSET_X = 3; // Right of main board (10 spacing)
    public const int PREVIEW_OFFSET_Y = 21; // Top area alignment
    
    // Multi-Material System Constants
    private const string BASE_MATERIAL = "Base";
    private const string COLOR_MATERIAL = "Color";
    private const string HIGHLIGHT1_MATERIAL = "Highlight1";
    
    public enum PieceType
    {
        I, O, T, S, Z, J, L
    }
    
    public enum GameState
    {
        WaitingForPlayer,
        WaitingForGrid,
        Playing,
        ClearingLines,
        GameOver
    }
    
    public enum FlashType
    {
        None = 0,
        LineClear = 1,
        GameOver = 2
    }
    
    public enum GameMode
    {
        Normal = 0,    // Default mode
        Speed = 1,     // 4x faster speed progression
        Timed = 2      // Time-limited rounds
    }
    
    /// <summary>
    /// Represents a single entry in the leaderboard
    /// </summary>
    public class LeaderboardEntry
    {
        public string Handle { get; set; }      // Player's persona handle
        public int Score { get; set; }
        public int Level { get; set; }
        public int Lines { get; set; }
        public DateTime Date { get; set; }
    }
    
    #endregion
    
    #region Block State Structure
    
    /// <summary>
    /// Comprehensive block state with history tracking and edge detection
    /// </summary>
    public class TetrisGameBlock
    {
        // Logic-owned current state (authoritative intent)
        public int PieceType = 0;                   // 0..6 = locked piece type
        public bool IsVisible = false;             // desired visibility (locked or active)
        public bool IsActivePiece = false;         // true when part of the falling piece
        public bool IsGhostPiece = false;          // true when part of the ghost preview
        public FlashType FlashMode = FlashType.None; // LineClear, GameOver, or None
        public float FlashRate = 2.0f;             // used for game over pulsing
        public float FlashStartTime = 0f;          // timestamp when flashing started

        public MeshComponent Mesh = null;          // reference to the mesh component
        public RigidBodyComponent RigidBody = null; // reference to the rigid body component
        public ObjectPrivate Object = null;        // reference to the object
        public Cluster Cluster = null;              // reference to the cluster

    
        // Visual last-applied state (edge-driven application)
        public bool LastVisible = false;           // last applied visibility to Mesh
        public int LastColorIndex = -1;            // last applied color index (-1 if none)
        public float LastEmissiveIntensity = 0f;   // last applied emissive intensity
    
        // Historical fields (kept for debugging/metrics)
        public float StateChangeTime = 0f;
        
        public float GetTimeSinceFlashStart(float currentTime) { return currentTime - FlashStartTime; }
        public float GetTimeSinceStateChange(float currentTime) { return currentTime - StateChangeTime; }
    }


    public class TetrisBackgroundBlock
    {
        public ObjectPrivate Object = null;        // reference to the object
        public MeshComponent Mesh = null;          // reference to the mesh component
        public Cluster Cluster = null;              // reference to the cluster
    }

    public class TetrisPreviewBlock
    {
        public ObjectPrivate Object = null;        // reference to the object
        public MeshComponent Mesh = null;          // reference to the mesh component
        public Cluster Cluster = null;              // reference to the cluster
    }

    /// <summary>
    /// Encapsulates all state and components for a single Tetris game board
    /// Allows multiple independent game boards to exist simultaneously
    /// </summary>
    public class TetrisGameBoard
    {
        // Grid dimensions - must match parent class constants
        private const int GRID_WIDTH = 10;
        private const int GRID_HEIGHT = 20;
        private const int PREVIEW_WIDTH = 4;
        private const int PREVIEW_HEIGHT = 4;
        
        // Board identification
        public int BoardIndex;
        public Vector GridOrigin;
        public AgentPrivate AssignedPlayer = null;
        
        // Seat components
        public Cluster SeatCluster = null;
        public RigidBodyComponent SeatRigidBody = null;
        
        // Spawning state
        public bool BlockSpawningInitiated = false;
        
        // Game state
        public GameState CurrentGameState = GameState.WaitingForPlayer;
        public int Score = 0;
        public int Level = 1;
        public int LinesCleared = 0;
        public long LastDropTime = 0;
        public float DropInterval = 1.0f;
        
        // Current piece state
        public PieceType CurrentPieceType = PieceType.I;
        public Vector CurrentPiecePosition = new Vector(0, 0, 0);
        public int CurrentPieceRotation = 0;
        public List<Vector> CurrentPieceBlocks = null;
        
        // Ghost piece state
        public Vector GhostPiecePosition = new Vector(0, 0, 0);
        public List<Vector> GhostPieceBlocks = null;
        public bool GhostPieceEnabled = true;  // Per-board ghost piece toggle
        
        
        // Line clearing state
        public List<int> LinesToClear = new List<int>();
        public long LineClearStartTime = 0;
        
        // Board-specific chat subscription
        private IEventSubscription BoardChatSubscription = null;
        private ScenePrivate ParentScene = null;
        
        // Grid arrays
        public TetrisGameBlock[,] GameBlocks = new TetrisGameBlock[GRID_WIDTH, GRID_HEIGHT];
        public TetrisBackgroundBlock[,] BackgroundBlocks = new TetrisBackgroundBlock[GRID_WIDTH, GRID_HEIGHT];
        public TetrisGameBlock[,] GhostBlocks = new TetrisGameBlock[GRID_WIDTH, GRID_HEIGHT];
        public TetrisPreviewBlock[,] PreviewBlocks = new TetrisPreviewBlock[PREVIEW_WIDTH, PREVIEW_HEIGHT];
        public TetrisBackgroundBlock[,] PreviewBackgroundBlocks = new TetrisBackgroundBlock[PREVIEW_WIDTH, PREVIEW_HEIGHT];
        
        // Spawning progress tracking
        public int BlocksSpawned = 0;
        public int BackgroundBlocksSpawned = 0;
        public int PreviewBlocksSpawned = 0;
        public int PreviewBackgroundBlocksSpawned = 0;
        public bool GridInitialized = false;
        
        // Preview system state
        public bool PreviewInitialized = false;
        public PieceType NextPieceType;
        public bool NextPieceInitialized = false;
        
        // Control system
        private IEventSubscription[] CommandSubscriptions = new IEventSubscription[10];
        
        // Key repeat system
        private bool LeftKeyHeld = false;
        private bool RightKeyHeld = false;
        private bool SoftDropKeyHeld = false;
        private int LeftKeyRepeatCount = 0;
        private int RightKeyRepeatCount = 0;
        private int SoftDropKeyRepeatCount = 0;
        private const int KEY_INITIAL_DELAY_CYCLES = 3;  // 3 cycles (300ms) before repeat starts
        private const int KEY_REPEAT_RATE_CYCLES = 1;    // 1 cycle (100ms) between repeats
        
        // Property accessor for consistency with existing code
        public GameState CurrentState 
        { 
            get { return CurrentGameState; } 
            set { CurrentGameState = value; }
        }
        
        // Hard drop protection flag
        public bool IsHardDropping = false;
        
        public TetrisGameBoard(int boardIndex, Vector gridOrigin, ScenePrivate parentScene)
        {
            BoardIndex = boardIndex;
            GridOrigin = gridOrigin;
            ParentScene = parentScene;
            
            // Initialize grid arrays
            for (int x = 0; x < GRID_WIDTH; x++)
            {
                for (int y = 0; y < GRID_HEIGHT; y++)
                {
                    GameBlocks[x, y] = new TetrisGameBlock();
                    BackgroundBlocks[x, y] = new TetrisBackgroundBlock();
                }
            }
            
            for (int x = 0; x < PREVIEW_WIDTH; x++)
            {
                for (int y = 0; y < PREVIEW_HEIGHT; y++)
                {
                    PreviewBlocks[x, y] = new TetrisPreviewBlock();
                    PreviewBackgroundBlocks[x, y] = new TetrisBackgroundBlock();
                }
            }
        }
        
        /// <summary>
        /// Reset all game state for this board while preserving object references
        /// </summary>
        public void ResetGameState(TetrisGame parentScript = null)
        {
            try
            {
                if (parentScript != null)
                    parentScript.Log.Write(string.Format("TetrisGame: ResetGameState - Starting for Board {0}", BoardIndex));
                
                // Ensure arrays are initialized
                if (GameBlocks == null)
                {
                    if (parentScript != null) parentScript.Log.Write(LogLevel.Error, "TetrisGame: ResetGameState - GameBlocks is null!");
                    return;
                }
                
                if (parentScript != null) parentScript.Log.Write(string.Format("TetrisGame: ResetGameState - GameBlocks array size: {0}x{1}", 
                    GameBlocks.GetLength(0), GameBlocks.GetLength(1)));
                
                // Reset block states while preserving object references
                if (parentScript != null) parentScript.Log.Write("TetrisGame: ResetGameState - Starting main grid loop");
                for (int x = 0; x < GRID_WIDTH; x++)
                {
                    for (int y = 0; y < GRID_HEIGHT; y++)
                    {
                        if (x == 0 && y == 0 && parentScript != null) 
                            parentScript.Log.Write("TetrisGame: ResetGameState - Processing first block (0,0)");
                        
                        // Ensure block exists
                        if (GameBlocks[x, y] == null)
                        {
                            if (parentScript != null) parentScript.Log.Write(string.Format("TetrisGame: ResetGameState - Creating new block at ({0},{1})", x, y));
                            GameBlocks[x, y] = new TetrisGameBlock();
                        }
                        
                        // Clear current state
                        GameBlocks[x, y].IsVisible = false;
                        GameBlocks[x, y].IsActivePiece = false;
                        GameBlocks[x, y].FlashMode = FlashType.None;
                    }
                }
                if (parentScript != null) parentScript.Log.Write("TetrisGame: ResetGameState - Main grid loop completed");
                
                // Force clear spawn area (top 4 rows)
                if (parentScript != null) parentScript.Log.Write("TetrisGame: ResetGameState - Clearing spawn area");
                for (int x = 0; x < GRID_WIDTH; x++)
                {
                    for (int y = GRID_HEIGHT - 4; y < GRID_HEIGHT; y++)
                    {
                        GameBlocks[x, y].IsVisible = false;
                        // Visual clearing will be handled by caller if needed
                    }
                }
                if (parentScript != null) parentScript.Log.Write("TetrisGame: ResetGameState - Spawn area cleared");
                
                // Reset current piece state
                if (parentScript != null) parentScript.Log.Write("TetrisGame: ResetGameState - Resetting piece state");
                CurrentPieceBlocks = null;
                CurrentPieceRotation = 0;
                
                // Reset drop timer to current time
                LastDropTime = Stopwatch.GetTimestamp();
                
                // Reset board game state
                if (parentScript != null) parentScript.Log.Write("TetrisGame: ResetGameState - Resetting board state variables");
                CurrentGameState = GameState.WaitingForPlayer;
                Score = 0;
                Level = 1;
                LinesCleared = 0;
                
                // Ensure LinesToClear is not null before clearing
                if (LinesToClear == null)
                {
                    if (parentScript != null) parentScript.Log.Write(LogLevel.Warning, 
                        string.Format("TetrisGame: Board {0} - LinesToClear was null, creating new list", BoardIndex));
                    LinesToClear = new List<int>();
                }
                else
                {
                    LinesToClear.Clear();
                }
                
                LineClearStartTime = 0;
                
                if (parentScript != null) parentScript.Log.Write(string.Format("TetrisGame: Board {0} game state reset completed successfully", BoardIndex));
            }
            catch (Exception ex)
            {
                if (parentScript != null) parentScript.Log.Write(LogLevel.Error, string.Format(
                    "TetrisGame: ResetGameState EXCEPTION on Board {0}: {1}\nStack: {2}", 
                    BoardIndex, ex.Message, ex.StackTrace));
                throw; // Re-throw to maintain original behavior
            }
        }
        
        /// <summary>
        /// Start a new game on this board
        /// </summary>
        /// <param name="parentScript">Reference to main TetrisGame script for method calls</param>
        public bool StartNewGame(TetrisGame parentScript)
        {
            parentScript.Log.Write(string.Format("TetrisGame: StartNewGame called for Board {0}", BoardIndex));
            
            // Check if board is ready
            if (!GridInitialized)
            {
                parentScript.Log.Write(string.Format("TetrisGame: Cannot start game on Board {0} - grid not initialized", BoardIndex));
                return false; // Cannot start game - grid not initialized
            }

            parentScript.Log.Write(string.Format("TetrisGame: Board {0} is ready - proceeding with game start", BoardIndex));
            
            // Ensure we're not in GameOver state
            if (CurrentState == GameState.GameOver)
            {
                parentScript.Log.Write(string.Format("TetrisGame: Board {0} - Clearing GameOver state before starting new game", BoardIndex));
                CurrentState = GameState.WaitingForPlayer;
            }

            // For Board 0, handle global game state
            if (BoardIndex == 0)
            {
                // Clean up any existing control subscriptions before starting (prevents double subscription bug)
                UnsubscribeFromControls();


                // Set global game state via parent script
                CurrentGameState = GameState.Playing;
            }
            
            // Reset all board state (calls ResetGameState internally)
            parentScript.Log.Write(string.Format("TetrisGame: Board {0} - About to call ResetGameState", BoardIndex));
            ResetGameState(parentScript);
            parentScript.Log.Write(string.Format("TetrisGame: Board {0} - ResetGameState completed successfully", BoardIndex));
            
            // Clear visual grid for this board
            parentScript.Log.Write(string.Format("TetrisGame: Board {0} - About to clear visual grid", BoardIndex));
            parentScript.ClearGridForBoard(this);
            
            // Unsubscribe from any existing controls first to prevent duplicates
            UnsubscribeFromControls();
            
            // Subscribe to player controls for this board
            parentScript.Log.Write(string.Format("TetrisGame: Board {0} - Subscribing to player controls", BoardIndex));
            SubscribeToControls(parentScript);
            
            // Initialize next piece system for this board
            if (IsPreviewInitialized())
            {
                parentScript.Log.Write(string.Format("TetrisGame: Board {0} - Initializing preview system", BoardIndex));
                InitializeNextPieceSystem(parentScript);
            }
            
            // Spawn first piece on this board
            // Log.Write(string.Format("TetrisGame: About to spawn first piece on Board {0}", BoardIndex));
            parentScript.SpawnNewPieceForBoard(this);
            
            // Set board state to playing
            CurrentState = GameState.Playing;
            // Log.Write(string.Format("TetrisGame: Set Board {0} state to Playing", BoardIndex));
            
            // Start timed mode if this is the first game and we're in timed mode
            if (parentScript.currentGameMode == GameMode.Timed && !parentScript.timedModeActive)
            {
                parentScript.timedModeActive = true;
                parentScript.timedModeStartTime = System.Diagnostics.Stopwatch.GetTimestamp();
                parentScript.Log.Write(string.Format("TetrisGame: Timed mode started - {0} seconds", parentScript.timedModeSeconds));
                
                // Announce to all players
                parentScript.SendChatToAll(string.Format("Timed round started! {0} seconds remaining.", parentScript.timedModeSeconds));
            }
            
            // Log.Write(string.Format("TetrisGame: Game started successfully on Board {0}", BoardIndex));
            
            return true; // Game started successfully
        }
        
        /// <summary>
        /// Called when a player sits down on this board's chair
        /// </summary>
        public void OnPlayerSitDown(AgentPrivate player, TetrisGame parentScript)
        {
            // Set the assigned player for this board
            AssignedPlayer = player;
            
            // Subscribe to chat for this player's board-specific commands
            if (ParentScene != null)
            {
                BoardChatSubscription = ParentScene.Chat.Subscribe(Chat.DefaultChannel, 
                    (ChatData data) => OnBoardChatMessage(data, parentScript));
                // Log.Write(string.Format("TetrisGame: Board {0} subscribed to chat for player {1}", BoardIndex, player.AgentInfo.Name));
            }
        }
        
        /// <summary>
        /// Update score and level based on lines cleared
        /// </summary>
        public void UpdateScore(TetrisGame parentScript, int linesCleared)
        {
            // Simple scoring system
            int[] lineScores = { 0, 100, 300, 500, 800 }; // 0, 1, 2, 3, 4 lines
            if (linesCleared > 0 && linesCleared <= 4)
            {
                Score += lineScores[linesCleared] * Level;
            }
            
            LinesCleared += linesCleared;
            
            // Level up every 10 lines
            if (LinesCleared / 10 > Level - 1)
            {
                Level++;
                // Apply speed multiplier based on game mode
                float speedMultiplier = (parentScript.currentGameMode == GameMode.Speed) ? 0.2f : 0.8f;
                DropInterval = Math.Max(0.1f, DropInterval * speedMultiplier);
                parentScript.Log.Write(string.Format("TetrisGame: Board {0} - Level up! Now level {1}, drop interval: {2:F2}s (mode: {3})", 
                    BoardIndex, Level, DropInterval, parentScript.currentGameMode));
            }
            
            parentScript.Log.Write(string.Format("TetrisGame: Board {0} - Score: {1}, Level: {2}, Lines: {3}", 
                BoardIndex, Score, Level, LinesCleared));
        }
        
        /// <summary>
        /// End game with game over sound
        /// </summary>
        public void EndGame(TetrisGame parentScript)
        {
            CurrentState = GameState.GameOver;
            
            parentScript.Log.Write(string.Format("TetrisGame: Board {0} - Game ended. Score: {1}, Lines: {2}, Level: {3}", 
                BoardIndex, Score, LinesCleared, Level));
            
            // Play game over sound
            parentScript.PlayGameOverSound(parentScript.GetBoardChairPosition(this));
            
            // Check if score qualifies for leaderboard
            if (AssignedPlayer != null && AssignedPlayer.IsValid && parentScript.IsHighScore(Score))
            {
                parentScript.UpdateLeaderboard(AssignedPlayer, Score, Level, LinesCleared);
                
                // Notify player of their achievement
                try
                {
                    AssignedPlayer.SendChat(string.Format("Congratulations {0}! You made the top 10 with {1} points!", 
                        AssignedPlayer.AgentInfo.Handle, Score));
                }
                catch (Exception ex)
                {
                    parentScript.Log.Write(LogLevel.Warning, string.Format("TetrisGame: Board {0} - Error sending leaderboard notification: {1}", 
                        BoardIndex, ex.Message));
                }
            }
            
            // Mark the final piece that caused game over for continuous flashing
            if (GridInitialized && CurrentPieceBlocks != null)
            {
                parentScript.MarkFinalPieceForGameOverFlash(this);
            }
            
            // Reset only the piece state, keep grid visible for flashing effect
            CurrentPieceBlocks = null;
            CurrentPieceRotation = 0;
            
            // Player controls cleanup handled by UnsubscribeFromControls()
            parentScript.Log.Write(string.Format("TetrisGame: Board {0} - Player controls deactivated", BoardIndex));
            
            // Note: Not automatically transitioning to WaitingForPlayer to prevent timing conflicts
            // Player can sit down again to restart when ready
            
            parentScript.Log.Write(string.Format("TetrisGame: Board {0} - Game ended - waiting for player to restart manually", BoardIndex));
        }
        
        /// <summary>
        /// End game without playing sound (for player stand up scenarios)
        /// </summary>
        private void EndGameQuietly(TetrisGame parentScript)
        {
            CurrentState = GameState.GameOver;
            
            parentScript.Log.Write(string.Format("TetrisGame: Board {0} - Game ended quietly (player left). Score: {1}, Lines: {2}, Level: {3}", 
                BoardIndex, Score, LinesCleared, Level));
            
            // NO game over sound for player leaving
            
            // Check if score qualifies for leaderboard
            if (AssignedPlayer != null && AssignedPlayer.IsValid && parentScript.IsHighScore(Score))
            {
                parentScript.UpdateLeaderboard(AssignedPlayer, Score, Level, LinesCleared);
                
                // Notify player of their achievement  
                try
                {
                    AssignedPlayer.SendChat(string.Format("Great job {0}! You made the top 10 with {1} points!", 
                        AssignedPlayer.AgentInfo.Handle, Score));
                }
                catch (Exception ex)
                {
                    parentScript.Log.Write(LogLevel.Warning, string.Format("TetrisGame: Board {0} - Error sending leaderboard notification: {1}", 
                        BoardIndex, ex.Message));
                }
            }
            
            // Mark the final piece that caused game over for continuous flashing
            if (GridInitialized && CurrentPieceBlocks != null)
            {
                parentScript.MarkFinalPieceForGameOverFlash(this);
            }
            
            // Reset only the piece state, keep grid visible for flashing effect
            CurrentPieceBlocks = null;
            CurrentPieceRotation = 0;
            
            // Player controls cleanup handled by UnsubscribeFromControls()
            parentScript.Log.Write(string.Format("TetrisGame: Board {0} - Player controls deactivated", BoardIndex));
            
            // Note: Not automatically transitioning to WaitingForPlayer to prevent timing conflicts
            // Player can sit down again to restart when ready
            
            parentScript.Log.Write(string.Format("TetrisGame: Board {0} - Game ended - waiting for player to restart manually", BoardIndex));
        }
        
        /// <summary>
        /// Called when a player stands up from this board's chair
        /// </summary>
        public void OnPlayerStandUp(TetrisGame parentScript, SitObjectData data)
        {
            // Log.Write(string.Format("TetrisGame: OnPlayerStandUp called for Board {0}", BoardIndex));
            // Log.Write(string.Format("TetrisGame: Board AssignedPlayer: {0}", AssignedPlayer != null ? AssignedPlayer.AgentInfo.Name : "null"));
            
            if (AssignedPlayer != null)
            {
                // Log.Write(string.Format("TetrisGame: Player {0} stood up from Board {1}", AssignedPlayer.AgentInfo.Name, BoardIndex));
                
                // Unsubscribe from board-specific chat
                if (BoardChatSubscription != null)
                {
                    BoardChatSubscription.Unsubscribe();
                    BoardChatSubscription = null;
                    // Log.Write(string.Format("TetrisGame: Board {0} unsubscribed from chat", BoardIndex));
                }
                
                if (AssignedPlayer != null)
                {
                    parentScript.Log.Write(string.Format("TetrisGame: Terminating game session due to player stand-up from Board {0}", BoardIndex));
                    
                    // Unsubscribe from all player controls immediately
                    UnsubscribeFromControls();
                    
                    // End the game quietly (no game over sound for leaving)
                    EndGameQuietly(parentScript);
                    
                    // Log.Write("TetrisGame: Game session terminated and currentPlayer cleared");
                }
                
                // Clear board assignment
                AssignedPlayer = null;
                // Log.Write(string.Format("TetrisGame: Cleared board assignment for Board {0}", BoardIndex));
            }
            
            // Reset board state to waiting for player
            CurrentState = GameState.WaitingForPlayer;
        }
        
        /// <summary>
        /// Subscribe to player controls for this board
        /// </summary>
        public void SubscribeToControls(TetrisGame parentScript)
        {
            // Log.Write(string.Format("TetrisGame: Board {0} SubscribeToControls called", BoardIndex));
            
            if (AssignedPlayer == null || !AssignedPlayer.IsValid)
            {
                // Log.Write(string.Format("TetrisGame: Board {0} has no valid player to subscribe to commands", BoardIndex));
                return;
            }
            
            // Log.Write(string.Format("TetrisGame: Board {0} player validation passed, proceeding with subscriptions", BoardIndex));
            
            try
            {
                // Log.Write(string.Format("TetrisGame: Board {0} starting command subscriptions", BoardIndex));
                
                // Subscribe to player commands using the proper agent.Client.SubscribeToCommand pattern
                CommandSubscriptions[0] = AssignedPlayer.Client.SubscribeToCommand("Keypad4", CommandAction.Pressed, 
                    (CommandData data) => HandleMoveLeftPressed(data, parentScript), null);     // A key → Move left
                    
                CommandSubscriptions[1] = AssignedPlayer.Client.SubscribeToCommand("Keypad6", CommandAction.Pressed, 
                    (CommandData data) => HandleMoveRightPressed(data, parentScript), null);    // D key → Move right
                    
                CommandSubscriptions[2] = AssignedPlayer.Client.SubscribeToCommand("Keypad2", CommandAction.Pressed, 
                    (CommandData data) => HandleSoftDrop(data, parentScript), null);     // S key → Soft drop
                    
                CommandSubscriptions[3] = AssignedPlayer.Client.SubscribeToCommand("Keypad8", CommandAction.Pressed, 
                    (CommandData data) => HandleSlowDown(data, parentScript), null);     // W key → Slow down
                    
                CommandSubscriptions[4] = AssignedPlayer.Client.SubscribeToCommand("SecondaryAction", CommandAction.Pressed, 
                    (CommandData data) => HandleRotate(data, parentScript), null); // R key → Rotate
                    
                CommandSubscriptions[5] = AssignedPlayer.Client.SubscribeToCommand("PrimaryAction", CommandAction.Pressed, 
                    (CommandData data) => HandleHardDrop(data, parentScript), null); // F key → Hard drop
                    
                CommandSubscriptions[6] = AssignedPlayer.Client.SubscribeToCommand("Action1", CommandAction.Pressed, 
                    (CommandData data) => HandleRotateCounterClockwise(data, parentScript), null); // 1 key → Rotate CCW
                    
                // Subscribe to key releases for repeat system
                CommandSubscriptions[7] = AssignedPlayer.Client.SubscribeToCommand("Keypad4", CommandAction.Released, 
                    (CommandData data) => HandleMoveLeftReleased(data), null);     // A key release
                    
                CommandSubscriptions[8] = AssignedPlayer.Client.SubscribeToCommand("Keypad6", CommandAction.Released, 
                    (CommandData data) => HandleMoveRightReleased(data), null);    // D key release
                    
                CommandSubscriptions[9] = AssignedPlayer.Client.SubscribeToCommand("Keypad2", CommandAction.Released, 
                    (CommandData data) => HandleSoftDropReleased(data), null);    // S key release
                    
                // Log.Write(string.Format("TetrisGame: Board {0} player controls activated", BoardIndex));
            }
            catch (Exception ex)
            {
                parentScript.Log.Write(LogLevel.Error, string.Format("TetrisGame: Board {0} error subscribing to player controls: {1}", BoardIndex, ex.Message));
                // Continue anyway - some controls might work
            }
        }
        
        /// <summary>
        /// Unsubscribe from player controls for this board
        /// </summary>
        public void UnsubscribeFromControls()
        {
            for (int i = 0; i < CommandSubscriptions.Length; i++)
            {
                if (CommandSubscriptions[i] != null)
                {
                    CommandSubscriptions[i].Unsubscribe();
                    CommandSubscriptions[i] = null;
                }
            }
            
            // Reset key repeat states
            LeftKeyHeld = false;
            RightKeyHeld = false;
            SoftDropKeyHeld = false;
            LeftKeyRepeatCount = 0;
            RightKeyRepeatCount = 0;
            SoftDropKeyRepeatCount = 0;
            
            // Log.Write(string.Format("TetrisGame: Board {0} unsubscribed from all player commands", BoardIndex));
        }
        
        /// <summary>
        /// Handle chat messages for this specific board
        /// Only responds to messages from the assigned player
        /// </summary>
        private void OnBoardChatMessage(ChatData data, TetrisGame parentScript)
        {
            // Only respond if message is from our assigned player
            if (AssignedPlayer == null || data.SourceId != AssignedPlayer.AgentInfo.SessionId)
                return;
                
            // Parse the message for board-specific commands
            string message = data.Message.Trim().ToLower();
            string[] chatWords = message.Split(' ');
            
            if (chatWords.Length < 2 || chatWords[0] != "/tet")
                return;
                
            // Build the command
            string command = chatWords[0] + " " + chatWords[1];
            
            // Log.Write(string.Format("TetrisGame: Board {0} processing command '{1}' from player {2}", 
            //     BoardIndex, command, AssignedPlayer.AgentInfo.Name));
            
            // Handle board-specific commands
            try
            {
                switch (command)
                {
                    case "/tet reset":
                    case "/tet restart":
                    case "/tet start":
                        bool gameStarted = this.StartNewGame(parentScript);
                        if (gameStarted)
                        {
                            AssignedPlayer.SendChat("New game started! Good luck!");
                            // Log.Write(string.Format("TetrisGame: Board {0} game reset for player {1}", 
                            //     BoardIndex, AssignedPlayer.AgentInfo.Name));
                        }
                        else
                        {
                            AssignedPlayer.SendChat("Cannot start game - board not ready.");
                        }
                        break;
                        
                    case "/tet ghost":
                        // Toggle ghost piece for this board
                        GhostPieceEnabled = !GhostPieceEnabled;
                        AssignedPlayer.SendChat(string.Format("Ghost piece {0}", GhostPieceEnabled ? "enabled" : "disabled"));
                        
                        // Update the visual state immediately if in game
                        if (CurrentState == GameState.Playing)
                        {
                            parentScript.UpdateGhostPiece(this);
                            parentScript.UpdateActivePieceFlags(this);
                        }
                        break;
                        
                    default:
                        // Ignore unknown commands - they might be handled globally
                        break;
                }
            }
            catch (Exception ex)
            {
                parentScript.Log.Write(LogLevel.Warning, string.Format("TetrisGame: Error processing board command on Board {0}: {1}", BoardIndex, ex.Message));
            }
        }
        
        /// <summary>
        /// Called when all blocks have been spawned and the grid is ready for this board
        /// </summary>
        public void OnGridInitializationComplete(TetrisGame parentScript)
        {
            GridInitialized = true;
            // Log.Write(string.Format("TetrisGame: Grid initialization complete for Board {0} - all {1} blocks spawned and ready", 
            //     BoardIndex, BlocksSpawned + BackgroundBlocksSpawned));
            
            // Handle Board 0 special cases
            if (BoardIndex == 0)
            {
                // Set global grid initialized flag for Board 0 (for backward compatibility)
                GridInitialized = true;
                // Log.Write("TetrisGame: Primary board (Board 0) grid initialized - global gridInitialized flag set");

                // Start debug rainbow mode if enabled
                if (parentScript.DebugRainbowMode)
                {
                    // Log.Write("TetrisGame: Starting rainbow debug mode to test material control");
                    parentScript.StartCoroutine(() => parentScript.RainbowDebugSequence(this));
                }
                
                // If a player is already waiting (sitting), start the game
                if (CurrentGameState == GameState.WaitingForGrid && AssignedPlayer != null)
                {
                    this.StartNewGame(parentScript);
                }
            }
        }
        
        // Command handlers
        private void HandleMoveLeftPressed(CommandData data, TetrisGame parentScript)
        {
            if (CurrentState != GameState.Playing || CurrentPieceBlocks == null) return;
            
            // Immediate move on press
            parentScript.MovePieceLeft(this);
            
            // Set repeat state
            LeftKeyHeld = true;
            LeftKeyRepeatCount = 0;
        }
        
        private void HandleMoveLeftReleased(CommandData data)
        {
            // Stop repeat
            LeftKeyHeld = false;
            LeftKeyRepeatCount = 0;
        }
        
        private void HandleMoveRightPressed(CommandData data, TetrisGame parentScript)
        {
            if (CurrentState != GameState.Playing || CurrentPieceBlocks == null) return;
            
            // Immediate move on press
            parentScript.MovePieceRight(this);
            
            // Set repeat state
            RightKeyHeld = true;
            RightKeyRepeatCount = 0;
        }
        
        private void HandleMoveRightReleased(CommandData data)
        {
            // Stop repeat
            RightKeyHeld = false;
            RightKeyRepeatCount = 0;
        }
        
        private void HandleSoftDrop(CommandData data, TetrisGame parentScript)
        {
            if (CurrentState != GameState.Playing || CurrentPieceBlocks == null) return;
            
            // Immediate move on press
            parentScript.MovePieceDown(this, true);  // triggeredByKey = true for soft drop
            
            // Set repeat state
            SoftDropKeyHeld = true;
            SoftDropKeyRepeatCount = 0;
        }
        
        private void HandleSoftDropReleased(CommandData data)
        {
            // Stop repeat
            SoftDropKeyHeld = false;
            SoftDropKeyRepeatCount = 0;
        }
        
        private void HandleSlowDown(CommandData data, TetrisGame parentScript)
        {
            if (CurrentState != GameState.Playing || CurrentPieceBlocks == null) return;
            
            // Log.Write("TetrisGame: W key (Keypad8) pressed - Slowing down piece");
            parentScript.SlowDownPiece(this);
        }
        
        private void HandleRotate(CommandData data, TetrisGame parentScript)
        {
            if (CurrentState != GameState.Playing || CurrentPieceBlocks == null) return;
            
            // Log.Write("TetrisGame: R key (SecondaryAction) pressed - Rotating clockwise");
            parentScript.RotatePiece(this, 1);
        }
        
        private void HandleHardDrop(CommandData data, TetrisGame parentScript)
        {
            if (CurrentState != GameState.Playing || CurrentPieceBlocks == null) return;
            
            // Log.Write("TetrisGame: F key (PrimaryAction) pressed - Hard drop");
            parentScript.HardDrop(this);
        }
        
        private void HandleRotateCounterClockwise(CommandData data, TetrisGame parentScript)
        {
            if (CurrentState != GameState.Playing || CurrentPieceBlocks == null) return;
            
            // Log.Write("TetrisGame: 1 key (Action1) pressed - Rotating counter-clockwise");
            parentScript.RotatePiece(this, -1);
        }
        
        /// <summary>
        /// Handle key repeat for smooth movement
        /// </summary>
        public void HandleKeyRepeat(TetrisGame parentScript)
        {
            try
            {
                // Handle left key repeat using simple counter approach
                if (LeftKeyHeld)
                {
                    LeftKeyRepeatCount++;
                    
                    // After initial delay, repeat at regular intervals
                    if (LeftKeyRepeatCount > KEY_INITIAL_DELAY_CYCLES)
                    {
                        // Check if it's time for a repeat (every KEY_REPEAT_RATE_CYCLES after initial delay)
                        int cyclesSinceInitialDelay = LeftKeyRepeatCount - KEY_INITIAL_DELAY_CYCLES;
                        if (cyclesSinceInitialDelay % KEY_REPEAT_RATE_CYCLES == 0)
                        {
                            parentScript.MovePieceLeft(this);
                        }
                    }
                }
                
                // Handle right key repeat using simple counter approach
                if (RightKeyHeld)
                {
                    RightKeyRepeatCount++;
                    
                    // After initial delay, repeat at regular intervals
                    if (RightKeyRepeatCount > KEY_INITIAL_DELAY_CYCLES)
                    {
                        // Check if it's time for a repeat (every KEY_REPEAT_RATE_CYCLES after initial delay)
                        int cyclesSinceInitialDelay = RightKeyRepeatCount - KEY_INITIAL_DELAY_CYCLES;
                        if (cyclesSinceInitialDelay % KEY_REPEAT_RATE_CYCLES == 0)
                        {
                            parentScript.MovePieceRight(this);
                        }
                    }
                }
                
                // Handle soft drop key repeat using simple counter approach  
                if (SoftDropKeyHeld)
                {
                    SoftDropKeyRepeatCount++;
                    
                    // After initial delay, repeat at regular intervals
                    if (SoftDropKeyRepeatCount > KEY_INITIAL_DELAY_CYCLES)
                    {
                        // Check if it's time for a repeat (every KEY_REPEAT_RATE_CYCLES after initial delay)
                        int cyclesSinceInitialDelay = SoftDropKeyRepeatCount - KEY_INITIAL_DELAY_CYCLES;
                        if (cyclesSinceInitialDelay % KEY_REPEAT_RATE_CYCLES == 0)
                        {
                            parentScript.MovePieceDown(this, true);  // triggeredByKey = true for soft drop repeat
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                parentScript.Log.Write(LogLevel.Error, string.Format("TetrisGame: Board {0} error in HandleKeyRepeat: {1}", BoardIndex, ex.Message));
                // Reset key states on error to prevent infinite loops
                LeftKeyHeld = false;
                RightKeyHeld = false;
                SoftDropKeyHeld = false;
                LeftKeyRepeatCount = 0;
                RightKeyRepeatCount = 0;
                SoftDropKeyRepeatCount = 0;
            }
        }
        
        /// <summary>
        /// Spawn all preview blocks for this board
        /// </summary>
        public void SpawnPreviewGridBlocks(TetrisGame parentScript)
        {
            // Log.Write(string.Format("TetrisGame: Starting preview grid block spawning for Board {0}...", BoardIndex));
            
            int totalPreviewBlocksToSpawn = PREVIEW_WIDTH * PREVIEW_HEIGHT;
            
            // Spawn blocks in batches to prevent throttling
            for (int batchStart = 0; batchStart < totalPreviewBlocksToSpawn; batchStart += parentScript.BatchSize)
            {
                SpawnPreviewBatch(parentScript, batchStart, totalPreviewBlocksToSpawn);
                
                // Wait between batches
                if (batchStart + parentScript.BatchSize < totalPreviewBlocksToSpawn)
                {
                    parentScript.Wait(TimeSpan.FromSeconds(parentScript.BatchDelay));
                }
            }
        }
        
        /// <summary>
        /// Spawn all preview background blocks for this board
        /// </summary>
        public void SpawnPreviewBackgroundGrid(TetrisGame parentScript)
        {
            // Log.Write(string.Format("TetrisGame: Starting preview background grid spawning for Board {0}...", BoardIndex));
            
            int totalPreviewBackgroundBlocksToSpawn = PREVIEW_WIDTH * PREVIEW_HEIGHT;
            
            // Spawn blocks in batches to prevent throttling
            for (int batchStart = 0; batchStart < totalPreviewBackgroundBlocksToSpawn; batchStart += parentScript.BatchSize)
            {
                SpawnPreviewBackgroundBatch(parentScript, batchStart, totalPreviewBackgroundBlocksToSpawn);
                
                // Wait between batches
                if (batchStart + parentScript.BatchSize < totalPreviewBackgroundBlocksToSpawn)
                {
                    parentScript.Wait(TimeSpan.FromSeconds(parentScript.BatchDelay));
                }
            }
        }
        
        private void SpawnPreviewBatch(TetrisGame parentScript, int startIndex, int totalBlocks)
        {
            int endIndex = Math.Min(startIndex + parentScript.BatchSize, totalBlocks);
            
            for (int i = startIndex; i < endIndex; i++)
            {
                int x = i % PREVIEW_WIDTH;
                int y = i / PREVIEW_WIDTH;
                
                SpawnPreviewBlockAt(parentScript, x, y);
            }
            
            // Log.Write(string.Format("TetrisGame: Spawned preview batch for Board {0}: blocks {1} to {2}", BoardIndex, startIndex, endIndex - 1));
        }
        
        private void SpawnPreviewBackgroundBatch(TetrisGame parentScript, int startIndex, int totalBlocks)
        {
            int endIndex = Math.Min(startIndex + parentScript.BatchSize, totalBlocks);
            
            for (int i = startIndex; i < endIndex; i++)
            {
                int x = i % PREVIEW_WIDTH;
                int y = i / PREVIEW_WIDTH;
                
                SpawnPreviewBackgroundBlockAt(parentScript, x, y);
            }
            
            // Log.Write(string.Format("TetrisGame: Spawned preview background batch for Board {0}: blocks {1} to {2}", BoardIndex, startIndex, endIndex - 1));
        }
        
        private void SpawnPreviewBlockAt(TetrisGame parentScript, int gridX, int gridY)
        {
            Sansar.Vector worldPosition = parentScript.GridToWorldPreview(this, gridX, gridY);
            
            try
            {
                ParentScene.CreateCluster(parentScript.GenericBlock, worldPosition, Sansar.Quaternion.Identity, Sansar.Vector.Zero,
                    (ScenePrivate.CreateClusterData data) =>
                    {
                        if (data.Success && data.ClusterReference != null)
                        {
                            OnPreviewBlockSpawned(parentScript, data.ClusterReference, gridX, gridY);
                        }
                        else
                        {
                            // Log.Write(LogLevel.Error, string.Format("TetrisGame: Failed to spawn preview block for Board {0} at ({1}, {2}): {3}", BoardIndex, gridX, gridY, data.Message));
                        }
                    });
            }
            catch (ThrottleException)
            {
                parentScript.Log.Write(LogLevel.Warning, string.Format("TetrisGame: Throttled preview block for Board {0} at ({1}, {2}) - retrying in 0.5 seconds", BoardIndex, gridX, gridY));
                parentScript.StartCoroutine(() => RetrySpawnPreviewBlock(parentScript, gridX, gridY));
            }
            catch (Exception ex)
            {
                parentScript.Log.Write(LogLevel.Error, string.Format("TetrisGame: Exception spawning preview block for Board {0} at ({1}, {2}): {3}", BoardIndex, gridX, gridY, ex.Message));
            }
        }
        
        private void SpawnPreviewBackgroundBlockAt(TetrisGame parentScript, int gridX, int gridY)
        {
            Sansar.Vector worldPosition = parentScript.GridToWorldPreviewBackground(this, gridX, gridY);
            
            try
            {
                ParentScene.CreateCluster(parentScript.GenericBlock, worldPosition, Sansar.Quaternion.Identity, Sansar.Vector.Zero,
                    (ScenePrivate.CreateClusterData data) =>
                    {
                        if (data.Success && data.ClusterReference != null)
                        {
                            OnPreviewBackgroundBlockSpawned(parentScript, data.ClusterReference, gridX, gridY);
                        }
                        else
                        {
                            // Log.Write(LogLevel.Error, string.Format("TetrisGame: Failed to spawn preview background block for Board {0} at ({1}, {2}): {3}", BoardIndex, gridX, gridY, data.Message));
                        }
                    });
            }
            catch (ThrottleException)
            {
                parentScript.Log.Write(LogLevel.Warning, string.Format("TetrisGame: Throttled preview background block for Board {0} at ({1}, {2}) - retrying in 0.5 seconds", BoardIndex, gridX, gridY));
                parentScript.StartCoroutine(() => RetrySpawnPreviewBackgroundBlock(parentScript, gridX, gridY));
            }
            catch (Exception ex)
            {
                parentScript.Log.Write(LogLevel.Error, string.Format("TetrisGame: Exception spawning preview background block for Board {0} at ({1}, {2}): {3}", BoardIndex, gridX, gridY, ex.Message));
            }
        }
        
        private void RetrySpawnPreviewBlock(TetrisGame parentScript, int gridX, int gridY)
        {
            parentScript.Wait(TimeSpan.FromSeconds(0.5));
            SpawnPreviewBlockAt(parentScript, gridX, gridY);
        }
        
        private void RetrySpawnPreviewBackgroundBlock(TetrisGame parentScript, int gridX, int gridY)
        {
            parentScript.Wait(TimeSpan.FromSeconds(0.5));
            SpawnPreviewBackgroundBlockAt(parentScript, gridX, gridY);
        }
        
        private void OnPreviewBlockSpawned(TetrisGame parentScript, Cluster cluster, int gridX, int gridY)
        {
            // Store cluster reference for cleanup
            this.PreviewBlocks[gridX, gridY].Cluster = cluster;
            
            // Store object and mesh references for direct control
            foreach (var objectPrivate in cluster.GetObjectPrivates())
            {
                // Store object reference
                this.PreviewBlocks[gridX, gridY].Object = objectPrivate;
                
                // Get and store mesh component for direct material control
                MeshComponent mesh;
                if (objectPrivate.TryGetFirstComponent(out mesh))
                {
                    this.PreviewBlocks[gridX, gridY].Mesh = mesh;
                    
                    // Check if mesh is scriptable and set initial hidden state
                    if (mesh.IsScriptable)
                    {
                        parentScript.SetPreviewBlockHidden(this, gridX, gridY);
                    }
                    else
                    {
                        // Log.Write(LogLevel.Error, string.Format("TetrisGame: Preview block ({0}, {1}) on Board {2}: Mesh is not scriptable", gridX, gridY, BoardIndex));
                    }
                }
                else
                {
                    // Log.Write(LogLevel.Error, string.Format("TetrisGame: Preview block ({0}, {1}) on Board {2}: No MeshComponent found", gridX, gridY, BoardIndex));
                }
                
                break; // Only need the first object
            }
            
            this.PreviewBlocksSpawned++;
            
            // Check if preview initialization is complete
            CheckPreviewInitialization(parentScript);
        }
        
        private void OnPreviewBackgroundBlockSpawned(TetrisGame parentScript, Cluster cluster, int gridX, int gridY)
        {
            // Store cluster reference for cleanup
            this.PreviewBackgroundBlocks[gridX, gridY].Cluster = cluster;
            
            // Store object and mesh references for direct control
            foreach (var objectPrivate in cluster.GetObjectPrivates())
            {
                // Store object reference
                this.PreviewBackgroundBlocks[gridX, gridY].Object = objectPrivate;
                
                // Get and store mesh component for direct material control
                MeshComponent mesh;
                if (objectPrivate.TryGetFirstComponent(out mesh))
                {
                    this.PreviewBackgroundBlocks[gridX, gridY].Mesh = mesh;
                    
                    // Set background block visible with proper background styling
                    if (mesh.IsScriptable)
                    {
                        parentScript.SetPreviewBackgroundBlockVisible(this, gridX, gridY);
                    }
                    else
                    {
                        // Log.Write(LogLevel.Error, string.Format("TetrisGame: Preview background block ({0}, {1}) on Board {2}: Mesh is not scriptable", gridX, gridY, BoardIndex));
                    }
                }
                else
                {
                    // Log.Write(LogLevel.Error, string.Format("TetrisGame: Preview background block ({0}, {1}) on Board {2}: No MeshComponent found", gridX, gridY, BoardIndex));
                }
                
                break; // Only need the first object
            }
            
            this.PreviewBackgroundBlocksSpawned++;
            
            // Check if preview initialization is complete
            CheckPreviewInitialization(parentScript);
        }
        
        // Preview initialization methods
        public bool IsPreviewInitialized()
        {
            return PreviewInitialized;
        }
        
        private void CheckPreviewInitialization(TetrisGame parentScript)
        {
            // Check if all preview blocks have been spawned
            int totalPreviewBlocks = PREVIEW_WIDTH * PREVIEW_HEIGHT;
            
            if (PreviewBlocksSpawned >= totalPreviewBlocks && 
                PreviewBackgroundBlocksSpawned >= totalPreviewBlocks &&
                !PreviewInitialized)
            {
                PreviewInitialized = true;
                parentScript.Log.Write(string.Format("TetrisGame: Preview system initialized for Board {0} - all {1} preview blocks spawned", 
                    BoardIndex, PreviewBlocksSpawned + PreviewBackgroundBlocksSpawned));
                
                // If a game is already in progress, initialize the preview system
                if (CurrentState == GameState.Playing && AssignedPlayer != null)
                {
                    parentScript.Log.Write(string.Format("TetrisGame: Board {0} - Game in progress, initializing preview system", BoardIndex));
                    InitializeNextPieceSystem(parentScript);
                }
            }
        }
        
        public void InitializeNextPieceSystem(TetrisGame parentScript)
        {
            // Log.Write(string.Format("TetrisGame: Initializing next piece preview system for Board {0}", BoardIndex));
            
            // Clear preview area
            parentScript.ClearPreview(this);
            
            // Generate first next piece
            GenerateNextPiece(parentScript);
            
            // Log.Write(string.Format("TetrisGame: Next piece preview system initialized for Board {0}", BoardIndex));
        }
        
        public void GenerateNextPiece(TetrisGame parentScript)
        {
            // Generate next piece
            PieceType[] pieceTypes = { PieceType.I, PieceType.O, PieceType.T, PieceType.S, PieceType.Z, PieceType.J, PieceType.L };
            NextPieceType = pieceTypes[parentScript.randomGenerator.Next(pieceTypes.Length)];
            
            // Update preview display if preview area is ready
            if (PreviewInitialized)
            {
                parentScript.ShowNextPiece(this, NextPieceType);
            }
            
            // Log.Write(string.Format("TetrisGame: Generated next piece: {0} for Board {1}", NextPieceType, BoardIndex));
        }
    }

    #endregion

    #region Game State Variables

    
    // Helper methods for TetrisGameBoard

    private List<AgentPrivate> allPlayers = new List<AgentPrivate>();
    
    // Multi-Board System
    private List<TetrisGameBoard> gameBoards = new List<TetrisGameBoard>();
    private const int MAX_BOARDS = 8;
    private const float BOARD_SPACING_X = 17.0f;
    
    // Board Spawn Queue System
    private Queue<TetrisGameBoard> boardSpawnQueue = new Queue<TetrisGameBoard>();
    private bool isSpawningBoard = false;
    
    // Game Mode System
    private GameMode currentGameMode = GameMode.Normal;
    private float timedModeSeconds = 0;
    private long timedModeStartTime = 0;
    private bool timedModeActive = false;
    private const double LINE_CLEAR_DURATION = 0.7; // 0.6s flash + 0.1s buffer
    
    
    // Pre-initialized Random to avoid blocking on entropy
    private Random randomGenerator = new Random();
    
    // Leaderboard persistence
    private DataStore leaderboardStore;
    private List<LeaderboardEntry> leaderboard = new List<LeaderboardEntry>();
    private readonly string leaderboardKey = "top10";
    
    // Background music system
    private PlayHandle currentMusicHandle;
    private int currentTrackIndex = 0;
    private bool musicEnabled = true;
    private float currentMusicVolume = 30.0f;
    private SoundResource[] musicTracks = new SoundResource[6];
    private SoundResource[] validMusicTracks;
    private bool musicSystemInitialized = false;
    
    
    #endregion
    
    
    #region Seat Detection Variables
    
    // Chat system variables
    private IEventSubscription chatSubscription;
    private Dictionary<string, string> commandsUsage;
    
    
    #endregion
        
    #region Piece Shape Definitions
    
    private static readonly Dictionary<PieceType, Vector[][]> PieceShapes = new Dictionary<PieceType, Vector[][]>
    {
        {PieceType.I, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(2, 0, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(0, 2, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(2, 0, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(0, 2, 0) }
        }},
        
        {PieceType.O, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) }
        }},
        
        {PieceType.T, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(1, 0, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(0, -1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(-1, 0, 0) }
        }},
        
        {PieceType.S, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(-1, 0, 0), new Vector(-1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(-1, 0, 0), new Vector(-1, 1, 0) }
        }},
        
        {PieceType.Z, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0), new Vector(-1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(1, 0, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(0, 1, 0), new Vector(-1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(1, 0, 0), new Vector(1, 1, 0) }
        }},
        
        {PieceType.J, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(-1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(1, -1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(-1, -1, 0) }
        }},
        
        {PieceType.L, new Vector[][]
        {
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(1, 1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(1, -1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(-1, -1, 0) },
            new Vector[] { new Vector(0, 0, 0), new Vector(0, -1, 0), new Vector(0, 1, 0), new Vector(-1, 1, 0) }
        }}
    };
    
    #endregion
    
    #region Color Definitions
    
    // Tetris piece colors
    private static readonly Dictionary<int, Sansar.Color> PieceColors = CreatePieceColors();
    
    private static Dictionary<int, Sansar.Color> CreatePieceColors()
    {
        var colors = new Dictionary<int, Sansar.Color>();
        colors.Add(0, new Sansar.Color(0.0f, 1.0f, 1.0f, 1.0f)); // I - Cyan
        colors.Add(1, new Sansar.Color(1.0f, 1.0f, 0.0f, 1.0f)); // O - Yellow
        colors.Add(2, new Sansar.Color(0.5f, 0.0f, 1.0f, 1.0f)); // T - Purple
        colors.Add(3, new Sansar.Color(0.0f, 1.0f, 0.0f, 1.0f)); // S - Green
        colors.Add(4, new Sansar.Color(1.0f, 0.0f, 0.0f, 1.0f)); // Z - Red
        colors.Add(5, new Sansar.Color(0.0f, 0.0f, 1.0f, 1.0f)); // J - Blue
        colors.Add(6, new Sansar.Color(1.0f, 0.5f, 0.0f, 1.0f)); // L - Orange
        return colors;
    }
    
    // Background grid color (solid black)
    private static readonly Sansar.Color BackgroundColor = new Sansar.Color(0.1f, 0.1f, 0.1f, 1.0f);
    
    #endregion
    
    #region Initialization
    
    public override void Init()
    {
        Log.Write("TetrisGame V2 - Unified Single Script Architecture");
        
        // Note: Original object visibility is now handled in Sansar editor
        // ChairOrigin parameter controls where chairs spawn
        
        // Note: We'll no longer use the original object's RigidBodyComponent for seat detection
        // All seat detection will be handled by spawned chairs
        
        // Initialize system-level components first
        InitializeChatCommands();
        
        // Set up global chat subscription (always active)
        chatSubscription = ScenePrivate.Chat.Subscribe(Chat.DefaultChannel, OnChatMessage);
        Log.Write(LogLevel.Info, "TetrisGame: Global chat interface activated - commands available to all users");
        
        // Initialize leaderboard persistence
        InitializeLeaderboard();
        
        // Initialize background music system
        InitializeBackgroundMusic();
        
        // Audio system now integrated directly into TetrisGame
        Log.Write(LogLevel.Info, "TetrisGame: Audio system initialized with integrated properties");
        
        // Initialize player tracking system
        InitializePlayerTracking();
        
        // Create game boards first, then initialize each board's state
        int boardsToCreate = DefaultBoardCount > 0 ? DefaultBoardCount : 1;
        Log.Write(string.Format("TetrisGame: Creating {0} initial game board(s)", boardsToCreate));
        
        for (int i = 0; i < boardsToCreate; i++)
        {
            Log.Write(string.Format("TetrisGame: Initializing Board {0} with uniform programmatic spawning", i));
            TetrisGameBoard board = CreateGameBoard(i);
            gameBoards.Add(board);
            
            // Board constructor already initializes grid arrays, so no separate InitializeGameGridState needed
            // Spawn chair for this board
            SpawnChairForBoard(board);
            // Note: Block spawning will start automatically when chair spawning completes (in OnChairSpawned)
        }
        
        // DEBUG: Test agent detection
        Log.Write(string.Format("DEBUG: Found {0} agents in scene", ScenePrivate.GetAgents().Count()));
        foreach (var agent in ScenePrivate.GetAgents())
        {
            string agentName = (agent != null && agent.AgentInfo != null) ? agent.AgentInfo.Name : "null";
            Log.Write(string.Format("DEBUG: Agent found: {0}", agentName));
        }
        
        // Validate resources
        if (GenericBlock == null)
        {
            Log.Write(LogLevel.Error, "TetrisGame: GenericBlock resource not set");
            return;
        }
        
        if (BackgroundBlock == null)
        {
            Log.Write(LogLevel.Error, "TetrisGame: BackgroundBlock resource not set");
            return;
        }
        
        if (ChairResource == null)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: ChairResource not set - /tet add command will not work");
        }
        
        // Start main game loop
        StartCoroutine(GameLoop);
        
        // Start board spawn queue processor
        StartCoroutine(ProcessBoardSpawnQueue);
        
        int totalBackgroundBlocks = gameBoards.Count * GRID_WIDTH * GRID_HEIGHT;
        int totalGameBlocks = gameBoards.Count * GRID_WIDTH * GRID_HEIGHT;
        Log.Write(string.Format("TetrisGame initialized - will spawn {0} background blocks and {1} game blocks across {2} board(s)", totalBackgroundBlocks, totalGameBlocks, gameBoards.Count));
        Log.Write(string.Format("TetrisGame: GridOrigin set to: ({0}, {1}, {2})", GridOrigin.X, GridOrigin.Y, GridOrigin.Z));
        Log.Write(string.Format("TetrisGame: ChairOrigin set to: ({0}, {1}, {2})", ChairOrigin.X, ChairOrigin.Y, ChairOrigin.Z));
        
        // Validate ChairOrigin parameter
        if (ChairOrigin.X == 0 && ChairOrigin.Y == 0 && ChairOrigin.Z == 0)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: ChairOrigin is set to <0,0,0> - please set this parameter in Sansar editor to position where you want the first chair to spawn");
        }
    }
    
    
    
    private void InitializeChatCommands()
    {
        commandsUsage = new Dictionary<string, string>
        {
            // Global commands (handled by main script)
            { "/tet help", "(Anyone) Show command list" },
            { "/tet add", "(Anyone) Add a new player game board" },
            { "/tet leaderboard", "(Anyone) Show top 10 scores" },
            { "/tet leader", "(Anyone) Show top 10 scores (short)" },
            { "/tet bg", "(Anyone) Toggle background music on/off" },
            { "/tet bg volume", "(Anyone) Set music volume (0-100)" },
            
            // Board-specific commands (handled by individual boards)
            { "/tet reset", "(Seated) Start new game on your board" },
            { "/tet restart", "(Seated) Start new game on your board" },
            { "/tet start", "(Seated) Start new game on your board" },
            { "/tet ghost", "(Seated) Toggle ghost piece preview on/off" },
            
            // Global game status command
            { "/tet score", "(Anyone) Show all active games" },
            
            // Game mode commands
            { "/tet mode", "(Anyone) Set game mode: 1=speed, 2=timed [seconds]" },
            { "/tet help mode", "(Anyone) List available game modes" }
        };
        
        Log.Write("TetrisGame: Chat interface commands initialized with /tet prefix - hybrid global/board system");
    }
    
    private void InitializeLeaderboard()
    {
        Log.Write(LogLevel.Info, "TetrisGame: InitializeLeaderboard() called");
        
        if (string.IsNullOrEmpty(LeaderboardStoreId))
        {
            Log.Write(LogLevel.Warning, "TetrisGame: LeaderboardStoreId is empty, leaderboard disabled");
            return;
        }
        
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Attempting to create DataStore with ID: '{0}'", LeaderboardStoreId));
        
        leaderboardStore = ScenePrivate.CreateDataStore(LeaderboardStoreId);
        if (leaderboardStore != null)
        {
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Leaderboard DataStore created successfully with id: '{0}'", LeaderboardStoreId));
            Log.Write(LogLevel.Info, string.Format("TetrisGame: DataStore.Id = '{0}'", leaderboardStore.Id));
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Attempting to restore leaderboard data with key: '{0}'", leaderboardKey));
            
            leaderboardStore.Restore<List<LeaderboardEntry>>(leaderboardKey, LoadLeaderboard);
        }
        else
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: FAILED to create leaderboard DataStore with id: '{0}'", LeaderboardStoreId));
            Log.Write(LogLevel.Error, "TetrisGame: Leaderboard functionality will be disabled");
        }
    }
    
    private void InitializeBackgroundMusic()
    {
        Log.Write(LogLevel.Info, "TetrisGame: InitializeBackgroundMusic() called");
        musicTracks[0] = BackgroundMusic1;
        musicTracks[1] = BackgroundMusic2;
        musicTracks[2] = BackgroundMusic3;
        musicTracks[3] = BackgroundMusic4;
        musicTracks[4] = BackgroundMusic5;
        musicTracks[5] = BackgroundMusic6;
        Log.Write(LogLevel.Info, "TetrisGame: InitializeBackgroundMusic() - Music tracks loaded");
        
        // Build array of only valid (non-null) tracks
        List<SoundResource> validTracks = new List<SoundResource>();
        for (int i = 0; i < musicTracks.Length; i++)
        {
            if (musicTracks[i] != null)
            {
                validTracks.Add(musicTracks[i]);
            }
        }
        
        validMusicTracks = validTracks.ToArray();
        
        if (validMusicTracks.Length == 0)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: No background music tracks provided. Background music will be disabled.");
            musicEnabled = false;
        }
        else
        {
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Found {0} valid background music tracks", validMusicTracks.Length));
            musicEnabled = BackgroundMusicEnabled;
        }
        
        currentMusicVolume = BackgroundMusicVolume;
        musicSystemInitialized = true;
        
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Background music initialized - Enabled: {0}, Volume: {1}%", musicEnabled, currentMusicVolume));
        
        // Start background music if enabled
        if (musicEnabled)
        {
            StartCoroutine(StartBackgroundMusic);
        }
    }
    
    // Old InitializeGameGridState() method removed - grid state now initialized in TetrisGameBoard constructor
    
    
    #endregion
    
    #region Player Session Management
        
    
    
    #endregion
    
    #region Multi-Player Tracking System
    
    private void InitializePlayerTracking()
    {
        Log.Write("TetrisGame: Initializing player tracking system");
        
        // Subscribe to user join/leave events
        ScenePrivate.User.Subscribe(User.AddUser, OnUserJoin);
        ScenePrivate.User.Subscribe(User.RemoveUser, OnUserLeave);
        
        // Initialize with any players already in the scene
        foreach (var agent in ScenePrivate.GetAgents())
        {
            if (agent != null && agent.IsValid)
            {
                allPlayers.Add(agent);
                Log.Write(string.Format("TetrisGame: Added existing player to tracking: {0}", agent.AgentInfo.Name));
            }
        }
        
        Log.Write(string.Format("TetrisGame: Player tracking initialized with {0} existing players", allPlayers.Count));
    }
    
    private void OnUserJoin(UserData data)
    {
        AgentPrivate agent = ScenePrivate.FindAgent(data.User);
        if (agent != null && agent.IsValid)
        {
            allPlayers.Add(agent);
            Log.Write(string.Format("TetrisGame: Player joined: {0} (Total players: {1})", agent.AgentInfo.Name, allPlayers.Count));
            
            // Show welcome hint to the new player
            StartCoroutine(() => ShowWelcomeHint(agent, data.User));

            // Loop through the game boards and Set collision state for currently visible blocks for this new player
            // Note: This is only necessary if the grid is already initialized
            for (int i = 0; i < gameBoards.Count; i++)
            {
                if (gameBoards[i].GridInitialized)
                {
                    InitializeCollisionForNewPlayer(agent);
                }
            }
        }
    }
    
    private void OnUserLeave(UserData data)
    {
        // Remove player from list by SessionId since agent reference may be invalid
        int removedCount = allPlayers.RemoveAll(agent => agent == null || !agent.IsValid || agent.AgentInfo.SessionId == data.User);
        if (removedCount > 0)
        {
            Log.Write(string.Format("TetrisGame: Player left (removed {0} entries, Total players: {1})", removedCount, allPlayers.Count));
        }
    }
    
    private void InitializeCollisionForNewPlayer(AgentPrivate player)
    {
        if (player == null || !player.IsValid)
            return;
            
        Log.Write(string.Format("TetrisGame: Initializing collision state for new player: {0}", player.AgentInfo.Name));
        
        // Initialize collision state for all boards
        if (gameBoards == null || gameBoards.Count == 0)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: No game boards initialized for collision setup");
            return;
        }
        
        // Set collision state for all boards' blocks based on their current visibility
        foreach (var board in gameBoards)
        {
            if (board == null || board.GameBlocks == null)
            {
                Log.Write(LogLevel.Warning, string.Format("TetrisGame: Board {0} GameBlocks array not initialized for collision setup", 
                    board != null ? board.BoardIndex : -1));
                continue;
            }
            
            for (int x = 0; x < GRID_WIDTH; x++)
            {
                for (int y = 0; y < GRID_HEIGHT; y++)
                {
                    RigidBodyComponent rigidBody = board.GameBlocks[x, y].RigidBody;
                    if (rigidBody != null)
                    {
                        bool isVisible = board.GameBlocks[x, y].IsVisible;
                        // true = ignore collision (pass through), false = enable collision (solid)
                        player.IgnoreCollisionWith(rigidBody, !isVisible);
                    }
                }
            }
        }
    }
    
    #endregion
    
    #region Multi-Board Management System
    
    /// <summary>
    /// Calculate world position for a game board based on its index
    /// </summary>
    private Vector GetBoardPosition(int boardIndex)
    {
        return new Vector(
            GridOrigin.X + (boardIndex * BOARD_SPACING_X),
            GridOrigin.Y,
            GridOrigin.Z
        );
    }
    
    /// <summary>
    /// Create a new game board at the specified index
    /// </summary>
    private TetrisGameBoard CreateGameBoard(int boardIndex)
    {
        Vector boardPosition = GetBoardPosition(boardIndex);
        TetrisGameBoard newBoard = new TetrisGameBoard(boardIndex, boardPosition, ScenePrivate);
        
        Log.Write(string.Format("TetrisGame: Created game board {0} at position {1}", boardIndex, boardPosition));
        return newBoard;
    }
    
    /// <summary>
    /// Find an available board index for a new board
    /// </summary>
    private int GetNextAvailableBoardIndex()
    {
        for (int i = 0; i < MAX_BOARDS; i++)
        {
            bool indexUsed = false;
            foreach (var board in gameBoards)
            {
                if (board.BoardIndex == i)
                {
                    indexUsed = true;
                    break;
                }
            }
            if (!indexUsed)
                return i;
        }
        return -1; // No available slots
    }
    
    /// <summary>
    /// Handle the /tet add command to create a new game board
    /// </summary>
    private void HandleAddBoardCommand(AgentPrivate sender)
    {
        try
        {
            // Check if we've reached the maximum number of boards
            if (gameBoards.Count >= MAX_BOARDS)
            {
                sender.SendChat(string.Format("Maximum number of game boards ({0}) already created.", MAX_BOARDS));
                return;
            }
            
            // Check if ChairResource is available
            if (ChairResource == null)
            {
                sender.SendChat("Chair resource not configured. Cannot create new game board.");
                return;
            }
            
            // Get next available board index
            int boardIndex = GetNextAvailableBoardIndex();
            if (boardIndex == -1)
            {
                sender.SendChat("No available board slots.");
                return;
            }
            
            // Create the new game board
            TetrisGameBoard newBoard = CreateGameBoard(boardIndex);
            gameBoards.Add(newBoard);
            
            // Spawn chair for the new board
            SpawnChairForBoard(newBoard);
            
            sender.SendChat(string.Format("Created new game board #{0} at position {1}. Total boards: {2}",
                boardIndex, newBoard.GridOrigin, gameBoards.Count));
            
            Log.Write(string.Format("TetrisGame: Player {0} created board #{1} (Total: {2})",
                sender.AgentInfo.Name, boardIndex, gameBoards.Count));
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Error in HandleAddBoardCommand: {0}", ex.Message));
            sender.SendChat("Error creating new game board.");
        }
    }
    
        
    // Note: Array synchronization methods removed since all boards use their own arrays
    
    /// <summary>
    /// Spawn a chair for the specified game board
    /// </summary>
    private void SpawnChairForBoard(TetrisGameBoard board)
    {
        if (board == null)
        {
            Log.Write(LogLevel.Error, "TetrisGame: Cannot spawn chair - board is null");
            return;
        }
        
        if (ChairResource == null)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Cannot spawn chair for Board {0} - ChairResource not set in editor", board.BoardIndex));
            return;
        }
        
        try
        {
            // Calculate chair position based on ChairOrigin parameter
            // Board 0 spawns at ChairOrigin, additional boards are spaced from there
            Vector chairPosition = new Vector(
                ChairOrigin.X + (board.BoardIndex * BOARD_SPACING_X),
                ChairOrigin.Y,
                ChairOrigin.Z
            );
            
            Log.Write(string.Format("TetrisGame: Spawning chair for Board {0} at {1} (ChairOrigin: {2}, Spacing: {3})", 
                board.BoardIndex, chairPosition, ChairOrigin, BOARD_SPACING_X));
            
            ScenePrivate.CreateCluster(ChairResource, chairPosition, Sansar.Quaternion.Identity, Sansar.Vector.Zero,
                (ScenePrivate.CreateClusterData data) =>
                {
                    if (data.Success && data.ClusterReference != null)
                    {
                        OnChairSpawned(data.ClusterReference, board);
                    }
                    else
                    {
                        Log.Write(LogLevel.Error, string.Format("TetrisGame: Failed to spawn chair for Board {0}", board.BoardIndex));
                    }
                });
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Error spawning chair for Board {0}: {1}", board.BoardIndex, ex.Message));
        }
    }
    
    /// <summary>
    /// Handle successful chair spawning
    /// </summary>
    private void OnChairSpawned(Cluster chairCluster, TetrisGameBoard board)
    {
        board.SeatCluster = chairCluster;
        
        Log.Write(string.Format("TetrisGame: Chair for Board {0} spawned successfully", board.BoardIndex));
        
        // Set up seat detection on the spawned chair
        try
        {
            var objects = chairCluster.GetObjectPrivates();
            Log.Write(string.Format("TetrisGame: Chair cluster for Board {0} contains {1} objects", board.BoardIndex, objects.Count()));
            
            int objectIndex = 0;
            foreach (var objectPrivate in objects)
            {
                Log.Write(string.Format("TetrisGame: Checking object {0} in chair cluster for Board {1}", objectIndex, board.BoardIndex));
                
                RigidBodyComponent chairRigidBody;
                if (objectPrivate.TryGetFirstComponent(out chairRigidBody))
                {
                    board.SeatRigidBody = chairRigidBody;
                    
                    // Check for sit points
                    try
                    {
                        var sitPoints = chairRigidBody.GetSitObjectInfo();
                        Log.Write(string.Format("TetrisGame: Chair for Board {0} has {1} sit points", board.BoardIndex, sitPoints.Length));
                        
                        if (sitPoints.Length > 0)
                        {
                            // Subscribe to sit/stand events for this specific board
                            chairRigidBody.SubscribeToSitObject(SitEventType.Start, (SitObjectData data) => OnPlayerSitDownBoard(data, board));
                            chairRigidBody.SubscribeToSitObject(SitEventType.End, (SitObjectData data) => board.OnPlayerStandUp(this, data));
                            
                            Log.Write(string.Format("TetrisGame: Seat detection enabled for Board {0} with {1} sit points", board.BoardIndex, sitPoints.Length));
                        }
                        else
                        {
                            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Chair for Board {0} has RigidBodyComponent but no sit points configured", board.BoardIndex));
                        }
                    }
                    catch (Exception ex)
                    {
                        Log.Write(LogLevel.Error, string.Format("TetrisGame: Error checking sit points for Board {0}: {1}", board.BoardIndex, ex.Message));
                    }
                    
                    break; // Only need the first object with RigidBodyComponent
                }
                else
                {
                    Log.Write(string.Format("TetrisGame: Object {0} in chair cluster for Board {1} has no RigidBodyComponent", objectIndex, board.BoardIndex));
                }
                objectIndex++;
            }
            
            if (board.SeatRigidBody == null)
            {
                Log.Write(LogLevel.Warning, string.Format("TetrisGame: No RigidBodyComponent found on spawned chair for Board {0} - chair resource needs RigidBodyComponent with sit points", board.BoardIndex));
                Log.Write(LogLevel.Info, "TetrisGame: SETUP REQUIRED - Please ensure your ChairResource has:");
                Log.Write(LogLevel.Info, "TetrisGame: 1. A RigidBodyComponent configured");
                Log.Write(LogLevel.Info, "TetrisGame: 2. Sit points configured on the RigidBodyComponent");
                Log.Write(LogLevel.Info, "TetrisGame: 3. No scripts attached to the chair resource");
                Log.Write(LogLevel.Info, "TetrisGame: 4. ChairOrigin parameter set to visible position in Sansar editor");
                Log.Write(LogLevel.Info, "TetrisGame: ALTERNATIVE - Use chat command '/tet start' to begin game without chair");
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Error setting up seat detection for Board {0}: {1}", board.BoardIndex, ex.Message));
        }
        
        // Add board to spawn queue instead of spawning immediately
        boardSpawnQueue.Enqueue(board);
        Log.Write(string.Format("TetrisGame: Board {0} added to spawn queue (queue size: {1})", 
            board.BoardIndex, boardSpawnQueue.Count));
    }
    
    /// <summary>
    /// Handle player sitting down on a specific board's chair
    /// </summary>
    private void OnPlayerSitDownBoard(SitObjectData data, TetrisGameBoard board)
    {
        Log.Write(string.Format("TetrisGame: OnPlayerSitDownBoard triggered for Board {0}", board.BoardIndex));
        
        AgentPrivate player = ScenePrivate.FindAgent(data.ObjectId);
        if (player != null && player.IsValid)
        {
            board.AssignedPlayer = player;
            Log.Write(string.Format("TetrisGame: Player {0} sat on Board {1} - player assigned successfully", player.AgentInfo.Name, board.BoardIndex));
            
            // Enable board-specific chat subscriptions
            Log.Write(string.Format("TetrisGame: Enabling board-specific chat subscriptions for {0} on Board {1}", player.AgentInfo.Name, board.BoardIndex));
            board.OnPlayerSitDown(player, this);
            
            // Update current state
            board.CurrentGameState = GameState.WaitingForGrid;
            Log.Write(string.Format("TetrisGame: Changed game state to WaitingForGrid for Board {0}", board.BoardIndex));

            // If board is in GameOver state, ensure it's properly reset first
            if (board.CurrentState == GameState.GameOver)
            {
                Log.Write(string.Format("TetrisGame: Board {0} is in GameOver state - resetting to WaitingForPlayer before starting new game", board.BoardIndex));
                board.CurrentState = GameState.WaitingForPlayer;
            }

            // Check if grid is ready and start game
            if (board.GridInitialized)
            {
                Log.Write(string.Format("TetrisGame: Grid ready for Board {0} - starting new game for {1}", board.BoardIndex, player.AgentInfo.Name));
                board.StartNewGame(this);
            }
            else
            {
                Log.Write(string.Format("TetrisGame: Grid not ready for Board {0} - game will start when grid initialization completes", board.BoardIndex));
            }
        }
        else
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Could not find valid player for sit event on Board {0}", board.BoardIndex));
        }
    }
    
    
    /// <summary>
    /// Spawn all blocks (background, game, preview) for a specific board
    /// </summary>
    private void SpawnBlocksForBoard(TetrisGameBoard board)
    {
        // Prevent duplicate spawning for the same board
        if (board.BlockSpawningInitiated)
        {
            Log.Write(string.Format("TetrisGame: Block spawning already initiated for Board {0} - skipping duplicate call", board.BoardIndex));
            return;
        }
        
        board.BlockSpawningInitiated = true;
        Log.Write(string.Format("TetrisGame: Starting block spawning for Board {0}", board.BoardIndex));
        
        // All boards (including Board 0) use the same spawning methods
        SpawnBackgroundGridForBoard(board);
        SpawnGridBlocksForBoard(board);
        SpawnGhostBlocksForBoard(board);
        
        // Spawn preview blocks for this board
        board.SpawnPreviewBackgroundGrid(this);
        board.SpawnPreviewGridBlocks(this);
        
        Log.Write(string.Format("TetrisGame: Block spawning initiated for Board {0}", board.BoardIndex));
    }
    
    #endregion
    
    #region Old Global Spawning Methods - REMOVED
    // Old global spawning methods removed to prevent conflicts with new board-specific system
    
    
    // Convert grid coordinates to world position for background blocks (offset to be visible)
    private Sansar.Vector GridToWorldBackground(TetrisGameBoard board, int gridX, int gridY)
    {
        // According to the comments: gridX->X (left/right), gridY->Z (up/down), Y is forward/back
        // For background blocks directly behind each game block, use same X,Z but different Y
        return new Sansar.Vector(
            board.GridOrigin.X + (gridX * BLOCK_SIZE),     // Same X position as game blocks
            board.GridOrigin.Y + 1.0f,                     // Try positive Y offset (forward instead of back)
            board.GridOrigin.Z + (gridY * BLOCK_SIZE)      // Same Z position as game blocks
        );
    }
    
    
    // Multi-Material System Methods
    
    private void SetBackgroundMultiMaterial(MeshComponent mesh)
    {
        if (mesh == null || !mesh.IsScriptable) return;
        
        try
        {
            // First try named materials
            var baseMaterial = mesh.GetRenderMaterial(BASE_MATERIAL);
            var highlight1Material = mesh.GetRenderMaterial(HIGHLIGHT1_MATERIAL);
            
            if (baseMaterial != null || highlight1Material != null)
            {
                // Named materials found - use them
                if (baseMaterial != null)
                {
                    var baseProps = baseMaterial.GetProperties();
                    if (baseMaterial.HasTint)
                        baseProps.Tint = BackgroundBaseColor; // Black
                    if (baseMaterial.HasEmissiveIntensity)
                        baseProps.EmissiveIntensity = 0.0f; // No emissive on base
                    baseMaterial.SetProperties(baseProps);
                }
                
                if (highlight1Material != null)
                {
                    var highlight1Props = highlight1Material.GetProperties();
                    if (highlight1Material.HasTint)
                        highlight1Props.Tint = BackgroundHighlightColor; // Dark gray
                    if (highlight1Material.HasEmissiveIntensity)
                        highlight1Props.EmissiveIntensity = BackgroundHighlightEmissive; // Slight glow
                    highlight1Material.SetProperties(highlight1Props);
                }
            }
            else
            {
                // No named materials - handle single material case
                // Since only 1 material with emissive is found, it's likely the Highlight1 material
                foreach (var material in mesh.GetRenderMaterials())
                {
                    var props = material.GetProperties();
                    
                    // Apply background highlight settings to the single material
                    if (material.HasTint)
                        props.Tint = BackgroundHighlightColor; // Dark gray
                        
                    if (material.HasEmissiveIntensity)
                        props.EmissiveIntensity = BackgroundHighlightEmissive; // Low emissive
                        
                    material.SetProperties(props);
                    break; // Only process first material since we know there's only one
                }
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Error setting background multi-material: {0}", ex.Message));
        }
    }
    
    private void SetGameBlockMultiMaterial(MeshComponent mesh, int pieceType, bool isActivePiece = false, bool isFlashing = false)
    {
        if (mesh == null || !mesh.IsScriptable) return;
        
        try
        {
            // SINGLE MATERIAL APPROACH - Like original system, apply color and emissive to all materials
            foreach (var material in mesh.GetRenderMaterials())
            {
                var props = material.GetProperties();
                
                if (material.HasTint)
                {
                    if (isFlashing)
                        props.Tint = Sansar.Color.White; // Flash override
                    else
                        props.Tint = PieceColors[pieceType]; // Piece color
                }
                
                if (material.HasEmissiveIntensity)
                {
                    if (isFlashing)
                        props.EmissiveIntensity = 15.0f; // Intense flash
                    else if (isActivePiece)
                        props.EmissiveIntensity = 2.0f;  // Active piece glow
                    else
                        props.EmissiveIntensity = 0.0f;  // No glow
                }
                
                material.SetProperties(props);
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Error setting game block material: {0}", ex.Message));
        }
    }
    
    #endregion
    
    
    #region Preview Area Spawning and Management
    

    
    // Convert grid coordinates to world position for preview area
    private Sansar.Vector GridToWorldPreview(TetrisGameBoard board, int gridX, int gridY)
    {
        return new Sansar.Vector(
            board.GridOrigin.X + ((PREVIEW_OFFSET_X + gridX) * BLOCK_SIZE),
            board.GridOrigin.Y,
            board.GridOrigin.Z + ((PREVIEW_OFFSET_Y + gridY) * BLOCK_SIZE)
        );
    }
    
    // Convert grid coordinates to world position for preview background blocks
    private Sansar.Vector GridToWorldPreviewBackground(TetrisGameBoard board, int gridX, int gridY)
    {
        return new Sansar.Vector(
            board.GridOrigin.X + ((PREVIEW_OFFSET_X + gridX) * BLOCK_SIZE),
            board.GridOrigin.Y + 1.0f, // Behind preview blocks
            board.GridOrigin.Z + ((PREVIEW_OFFSET_Y + gridY) * BLOCK_SIZE)
        );
    }
    
    // Board-specific coordinate conversion methods
    
    /// <summary>
    /// Convert grid coordinates to world position for a specific board
    /// </summary>
    private Sansar.Vector GridToWorldForBoard(int gridX, int gridY, TetrisGameBoard board)
    {
        return new Sansar.Vector(
            board.GridOrigin.X + (gridX * BLOCK_SIZE),
            board.GridOrigin.Y,
            board.GridOrigin.Z + (gridY * BLOCK_SIZE)
        );
    }
    
    /// <summary>
    /// Convert grid coordinates to world position for background blocks on a specific board
    /// </summary>
    private Sansar.Vector GridToWorldBackgroundForBoard(int gridX, int gridY, TetrisGameBoard board)
    {
        return new Sansar.Vector(
            board.GridOrigin.X + (gridX * BLOCK_SIZE),
            board.GridOrigin.Y + 1.0f, // Behind game blocks
            board.GridOrigin.Z + (gridY * BLOCK_SIZE)
        );
    }
    
    /// <summary>
    /// Convert grid coordinates to world position for preview blocks on a specific board
    /// </summary>
    private Sansar.Vector GridToWorldPreviewForBoard(int gridX, int gridY, TetrisGameBoard board)
    {
        return new Sansar.Vector(
            board.GridOrigin.X + ((PREVIEW_OFFSET_X + gridX) * BLOCK_SIZE),
            board.GridOrigin.Y,
            board.GridOrigin.Z + ((PREVIEW_OFFSET_Y + gridY) * BLOCK_SIZE)
        );
    }
    
    /// <summary>
    /// Get the chair position for a specific board
    /// </summary>
    private Sansar.Vector GetBoardChairPosition(TetrisGameBoard board)
    {
        return new Sansar.Vector(
            ChairOrigin.X + (board.BoardIndex * BOARD_SPACING_X),
            ChairOrigin.Y,
            ChairOrigin.Z
        );
    }
    
    #endregion
    
    #region Board-Specific Block Spawning Methods
    
    /// <summary>
    /// Spawn background grid blocks for a specific board
    /// </summary>
    private void SpawnBackgroundGridForBoard(TetrisGameBoard board)
    {
        Log.Write(string.Format("TetrisGame: Starting background grid spawning for Board {0}...", board.BoardIndex));
        
        int totalBlocksToSpawn = GRID_WIDTH * GRID_HEIGHT;
        
        // Spawn blocks in batches to prevent throttling
        for (int batchStart = 0; batchStart < totalBlocksToSpawn; batchStart += BatchSize)
        {
            SpawnBackgroundBatchForBoard(board, batchStart);
            
            // Wait between batches
            if (batchStart + BatchSize < totalBlocksToSpawn)
            {
                Wait(TimeSpan.FromSeconds(BatchDelay));
            }
        }
    }
    
    /// <summary>
    /// Spawn a batch of background blocks for a specific board
    /// </summary>
    private void SpawnBackgroundBatchForBoard(TetrisGameBoard board, int startIndex)
    {
        int totalBlocksToSpawn = GRID_WIDTH * GRID_HEIGHT;
        int endIndex = Math.Min(startIndex + BatchSize, totalBlocksToSpawn);
        
        for (int i = startIndex; i < endIndex; i++)
        {
            int x = i % GRID_WIDTH;
            int y = i / GRID_WIDTH;
            
            SpawnBackgroundBlockAtForBoard(board, x, y);
        }
    }
    
    /// <summary>
    /// Spawn a single background block for a specific board
    /// </summary>
    private void SpawnBackgroundBlockAtForBoard(TetrisGameBoard board, int gridX, int gridY)
    {
        Sansar.Vector worldPosition = GridToWorldBackgroundForBoard(gridX, gridY, board);
        
        try
        {
            ScenePrivate.CreateCluster(BackgroundBlock, worldPosition, Sansar.Quaternion.Identity, Sansar.Vector.Zero,
                (ScenePrivate.CreateClusterData data) =>
                {
                    if (data.Success && data.ClusterReference != null)
                    {
                        OnBackgroundBlockSpawnedForBoard(data.ClusterReference, board, gridX, gridY);
                    }
                    else
                    {
                        Log.Write(LogLevel.Error, string.Format("TetrisGame: Failed to spawn background block for Board {0} at ({1}, {2}): {3}", board.BoardIndex, gridX, gridY, data.Message));
                    }
                });
        }
        catch (ThrottleException)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Throttled background block for Board {0} at ({1}, {2}) - retrying in 0.5 seconds", board.BoardIndex, gridX, gridY));
            StartCoroutine(() => RetrySpawnBackgroundBlockForBoard(board, gridX, gridY));
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Exception spawning background block for Board {0} at ({1}, {2}): {3}", board.BoardIndex, gridX, gridY, ex.Message));
        }
    }
    
    /// <summary>
    /// Handle successful background block spawning for a specific board
    /// </summary>
    private void OnBackgroundBlockSpawnedForBoard(Cluster cluster, TetrisGameBoard board, int gridX, int gridY)
    {
        // Store cluster reference in board's array
        board.BackgroundBlocks[gridX, gridY].Cluster = cluster;
        
        // Store object and mesh references for direct control
        foreach (var objectPrivate in cluster.GetObjectPrivates())
        {
            board.BackgroundBlocks[gridX, gridY].Object = objectPrivate;
            
            MeshComponent mesh;
            if (objectPrivate.TryGetFirstComponent(out mesh))
            {
                board.BackgroundBlocks[gridX, gridY].Mesh = mesh;
                
                // Set background color and make visible
                if (mesh.IsScriptable)
                {
                    // Add small delay to ensure Sansar has fully initialized named materials
                    StartCoroutine(() => {
                        Wait(TimeSpan.FromMilliseconds(50)); // Small delay for material initialization
                        SetBackgroundBlockVisibleForBoard(board, gridX, gridY);
                    });
                }
                board.BackgroundBlocksSpawned++;
                
                // Check if this board's grid spawning is complete
                int totalBlocksForBoard = GRID_WIDTH * GRID_HEIGHT;
                if (board.BlocksSpawned >= totalBlocksForBoard && board.BackgroundBlocksSpawned >= totalBlocksForBoard)
                {
                    board.OnGridInitializationComplete(this);
                }
                
                break;
            }
        }
    }
    
    /// <summary>
    /// Retry spawning a background block for a specific board after throttle delay
    /// </summary>
    private void RetrySpawnBackgroundBlockForBoard(TetrisGameBoard board, int gridX, int gridY)
    {
        Wait(TimeSpan.FromSeconds(0.5));
        SpawnBackgroundBlockAtForBoard(board, gridX, gridY);
    }
    
    /// <summary>
    /// Spawn game grid blocks for a specific board
    /// </summary>
    private void SpawnGridBlocksForBoard(TetrisGameBoard board)
    {
        Log.Write(string.Format("TetrisGame: Starting grid block spawning for Board {0}...", board.BoardIndex));
        
        int totalBlocksToSpawn = GRID_WIDTH * GRID_HEIGHT;
        
        // Spawn blocks in batches to prevent throttling
        for (int batchStart = 0; batchStart < totalBlocksToSpawn; batchStart += BatchSize)
        {
            SpawnBatchForBoard(board, batchStart);
            
            // Wait between batches
            if (batchStart + BatchSize < totalBlocksToSpawn)
            {
                Wait(TimeSpan.FromSeconds(BatchDelay));
            }
        }
    }
    
    /// <summary>
    /// Spawn a batch of game blocks for a specific board
    /// </summary>
    private void SpawnBatchForBoard(TetrisGameBoard board, int startIndex)
    {
        int totalBlocksToSpawn = GRID_WIDTH * GRID_HEIGHT;
        int endIndex = Math.Min(startIndex + BatchSize, totalBlocksToSpawn);
        
        for (int i = startIndex; i < endIndex; i++)
        {
            int x = i % GRID_WIDTH;
            int y = i / GRID_WIDTH;
            
            SpawnBlockAtForBoard(board, x, y);
        }
    }
    
    /// <summary>
    /// Spawn a single game block for a specific board
    /// </summary>
    private void SpawnBlockAtForBoard(TetrisGameBoard board, int gridX, int gridY)
    {
        Sansar.Vector worldPosition = GridToWorldForBoard(gridX, gridY, board);
        
        try
        {
            ScenePrivate.CreateCluster(GenericBlock, worldPosition, Sansar.Quaternion.Identity, Sansar.Vector.Zero,
                (ScenePrivate.CreateClusterData data) =>
                {
                    if (data.Success && data.ClusterReference != null)
                    {
                        OnBlockSpawnedForBoard(data.ClusterReference, board, gridX, gridY);
                    }
                    else
                    {
                        Log.Write(LogLevel.Error, string.Format("TetrisGame: Failed to spawn block for Board {0} at ({1}, {2}): {3}", board.BoardIndex, gridX, gridY, data.Message));
                    }
                });
        }
        catch (ThrottleException)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Throttled block for Board {0} at ({1}, {2}) - retrying in 0.5 seconds", board.BoardIndex, gridX, gridY));
            StartCoroutine(() => RetrySpawnBlockForBoard(board, gridX, gridY));
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Exception spawning block for Board {0} at ({1}, {2}): {3}", board.BoardIndex, gridX, gridY, ex.Message));
        }
    }
    
    /// <summary>
    /// Handle successful block spawning for a specific board
    /// </summary>
    private void OnBlockSpawnedForBoard(Cluster cluster, TetrisGameBoard board, int gridX, int gridY)
    {
        // Store cluster reference in board's array
        board.GameBlocks[gridX, gridY].Cluster = cluster;
        
        // Store object and mesh references for direct control
        foreach (var objectPrivate in cluster.GetObjectPrivates())
        {
            board.GameBlocks[gridX, gridY].Object = objectPrivate;
            
            MeshComponent mesh;
            if (objectPrivate.TryGetFirstComponent(out mesh))
            {
                board.GameBlocks[gridX, gridY].Mesh = mesh;
                
                // Get and store rigid body component for collision control
                RigidBodyComponent rigidBody;
                if (objectPrivate.TryGetFirstComponent(out rigidBody))
                {
                    board.GameBlocks[gridX, gridY].RigidBody = rigidBody;
                }
                
                // Set initial hidden state
                if (mesh.IsScriptable)
                {
                    mesh.SetIsVisible(false);
                }
                board.BlocksSpawned++;
                
                // Check if this board's grid spawning is complete
                int totalBlocksForBoard = GRID_WIDTH * GRID_HEIGHT;
                if (board.BlocksSpawned >= totalBlocksForBoard && board.BackgroundBlocksSpawned >= totalBlocksForBoard)
                {
                    board.OnGridInitializationComplete(this);
                }
                
                break;
            }
        }
    }
    
    /// <summary>
    /// Retry spawning a block for a specific board after throttle delay
    /// </summary>
    private void RetrySpawnBlockForBoard(TetrisGameBoard board, int gridX, int gridY)
    {
        Wait(TimeSpan.FromSeconds(0.5));
        SpawnBlockAtForBoard(board, gridX, gridY);
    }
    
    /// <summary>
    /// Spawn ghost blocks for a specific board
    /// </summary>
    private void SpawnGhostBlocksForBoard(TetrisGameBoard board)
    {
        // Skip if ghost blocks are not configured
        if (GhostBlock == null)
        {
            Log.Write(string.Format("TetrisGame: Skipping ghost block spawning for Board {0} - GhostBlock ClusterResource not set", board.BoardIndex));
            return;
        }
        
        Log.Write(string.Format("TetrisGame: Starting ghost block spawning for Board {0}...", board.BoardIndex));
        
        int totalBlocksToSpawn = GRID_WIDTH * GRID_HEIGHT;
        
        // Spawn ghost blocks in batches to prevent throttling
        for (int batchStart = 0; batchStart < totalBlocksToSpawn; batchStart += BatchSize)
        {
            SpawnGhostBatchForBoard(board, batchStart);
            
            // Wait between batches
            if (batchStart + BatchSize < totalBlocksToSpawn)
            {
                Wait(TimeSpan.FromSeconds(BatchDelay));
            }
        }
    }
    
    /// <summary>
    /// Spawn a batch of ghost blocks for a specific board
    /// </summary>
    private void SpawnGhostBatchForBoard(TetrisGameBoard board, int startIndex)
    {
        int totalBlocksToSpawn = GRID_WIDTH * GRID_HEIGHT;
        int endIndex = Math.Min(startIndex + BatchSize, totalBlocksToSpawn);
        
        for (int i = startIndex; i < endIndex; i++)
        {
            int x = i % GRID_WIDTH;
            int y = i / GRID_WIDTH;
            
            SpawnGhostBlockAtForBoard(board, x, y);
        }
    }
    
    /// <summary>
    /// Spawn a single ghost block for a specific board
    /// </summary>
    private void SpawnGhostBlockAtForBoard(TetrisGameBoard board, int gridX, int gridY)
    {
        Sansar.Vector worldPosition = GridToWorldForBoard(gridX, gridY, board);
        
        try
        {
            ScenePrivate.CreateCluster(GhostBlock, worldPosition, Sansar.Quaternion.Identity, Sansar.Vector.Zero,
                (ScenePrivate.CreateClusterData data) =>
                {
                    if (data.Success && data.ClusterReference != null)
                    {
                        OnGhostBlockSpawnedForBoard(data.ClusterReference, board, gridX, gridY);
                    }
                    else
                    {
                        Log.Write(LogLevel.Warning, string.Format("TetrisGame: Failed to spawn ghost block for Board {0} at {1},{2}", board.BoardIndex, gridX, gridY));
                        RetrySpawnGhostBlockForBoard(board, gridX, gridY);
                    }
                });
        }
        catch (ThrottleException)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Throttled while spawning ghost block for Board {0} at {1},{2} - retrying", board.BoardIndex, gridX, gridY));
            RetrySpawnGhostBlockForBoard(board, gridX, gridY);
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Error spawning ghost block for Board {0} at {1},{2}: {3}", board.BoardIndex, gridX, gridY, ex.Message));
        }
    }
    
    /// <summary>
    /// Called when a ghost block is successfully spawned for a board
    /// </summary>
    private void OnGhostBlockSpawnedForBoard(Cluster cluster, TetrisGameBoard board, int gridX, int gridY)
    {
        if (gridX < 0 || gridX >= GRID_WIDTH || gridY < 0 || gridY >= GRID_HEIGHT)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Ghost block spawned with invalid coordinates {0},{1} for Board {2}", gridX, gridY, board.BoardIndex));
            return;
        }
        
        // Initialize ghost block data
        board.GhostBlocks[gridX, gridY] = new TetrisGameBlock();
        board.GhostBlocks[gridX, gridY].Cluster = cluster;
        board.GhostBlocks[gridX, gridY].IsGhostPiece = true;
        
        // Store object and mesh references for direct control
        foreach (var objectPrivate in cluster.GetObjectPrivates())
        {
            board.GhostBlocks[gridX, gridY].Object = objectPrivate;
            
            // Get mesh component
            MeshComponent mesh;
            if (objectPrivate.TryGetFirstComponent(out mesh))
            {
                board.GhostBlocks[gridX, gridY].Mesh = mesh;
                
                // Set initial hidden state
                if (mesh.IsScriptable)
                {
                    mesh.SetIsVisible(false);
                }
            }
            
            break; // Only need the first object
        }
    }
    
    /// <summary>
    /// Retry spawning a ghost block for a specific board after throttle delay
    /// </summary>
    private void RetrySpawnGhostBlockForBoard(TetrisGameBoard board, int gridX, int gridY)
    {
        Wait(TimeSpan.FromSeconds(0.5));
        SpawnGhostBlockAtForBoard(board, gridX, gridY);
    }
    
    /// <summary>
    /// Set background block visible for a specific board with proper Base + Highlight1 materials
    /// </summary>
    private void SetBackgroundBlockVisibleForBoard(TetrisGameBoard board, int gridX, int gridY)
    {
        MeshComponent mesh = board.BackgroundBlocks[gridX, gridY].Mesh;
        if (mesh != null && mesh.IsScriptable)
        {
            mesh.SetIsVisible(true);
            
            // Use the existing multi-material system for proper Base + Highlight1 setup
            if (EnableMultiMaterial)
            {
                SetBackgroundMultiMaterial(mesh);
            }
            else
            {
                // Fallback to single-material system using BackgroundColor
                foreach (var material in mesh.GetRenderMaterials())
                {
                    var props = material.GetProperties();
                    if (material.HasTint)
                        props.Tint = BackgroundColor;
                    if (material.HasEmissiveIntensity)
                        props.EmissiveIntensity = 0.0f;
                    material.SetProperties(props);
                }
            }
        }
    }
    
    #endregion
    
    #region Direct Material Control Methods
    
        
    public void SetBlockVisibility(TetrisGameBoard board, int gridX, int gridY, bool visibility)
    {
        if (!IsBlockInBounds(gridX, gridY))
            return;
        
        if (board == null || board.GameBlocks == null)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Board {0} GameBlocks array not initialized for block ({1},{2})", 
                board != null ? board.BoardIndex : -1, gridX, gridY));
            return;
        }
        
        MeshComponent mesh = board.GameBlocks[gridX, gridY].Mesh;

        if (mesh == null)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Block ({0},{1}) mesh reference is null in Board {2} GameBlocks array", 
                gridX, gridY, board.BoardIndex));
            return;
        }
        
        if (!mesh.IsScriptable)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Block ({0},{1}) mesh is not scriptable on Board {2}", 
                gridX, gridY, board.BoardIndex));
            return;
        }
        
        try
        {
            mesh.SetIsVisible(visibility);
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Error setting block ({0}, {1}) visibility on Board {2}: {3}", 
                gridX, gridY, board.BoardIndex, ex.Message));
        }
    }

    // set block color
    public void SetBlockColorAndEmissive(TetrisGameBoard board, int gridX, int gridY, int pieceType, float emissiveIntensity)
    {
        if (!IsBlockInBounds(gridX, gridY))
            return;
        
        if (!PieceColors.ContainsKey(pieceType))
            return;
        
        if (board == null || board.GameBlocks == null)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Board {0} GameBlocks array not initialized for block ({1},{2})", 
                board != null ? board.BoardIndex : -1, gridX, gridY));
            return;
        }
        
        MeshComponent mesh = board.GameBlocks[gridX, gridY].Mesh;
        if (mesh == null)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Block ({0},{1}) mesh reference is null in Board {2} GameBlocks array", 
                gridX, gridY, board.BoardIndex));
            return;
        }
        
        if (!mesh.IsScriptable)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Block ({0},{1}) mesh is not scriptable on Board {2}", 
                gridX, gridY, board.BoardIndex));
            return;
        }
        
        try
        {
            if (EnableMultiMaterial)
            {
                // Use new multi-material system
                bool isActive = (emissiveIntensity > 0.0f);
                SetGameBlockMultiMaterial(mesh, pieceType, isActive);
            }
            else
            {
                // Fallback to single-material system
                Sansar.Color blockColor = PieceColors[pieceType];
                
                foreach (var material in mesh.GetRenderMaterials())
                {
                    var props = material.GetProperties();
                    
                    if (material.HasTint)
                    {
                        props.Tint = blockColor;
                    }

                    if (material.HasEmissiveIntensity)
                    {
                        props.EmissiveIntensity = emissiveIntensity;
                    }
                    
                    material.SetProperties(props);
                }
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Error, string.Format("TetrisGame: Error setting block color ({0}, {1}): {2}", gridX, gridY, ex.Message));
        }
    }
    
    
    
    #endregion
    
    #region Collision Management Methods
    
    /// <summary>
    /// Set collision behavior for a specific game block
    /// </summary>
    /// <param name="board">The game board</param>
    /// <param name="gridX">Grid X position</param>
    /// <param name="gridY">Grid Y position</param>
    /// <param name="enableCollision">True = solid collision, False = pass through</param>
    private void SetBlockCollision(TetrisGameBoard board, int gridX, int gridY, bool enableCollision)
    {
        if (!IsBlockInBounds(gridX, gridY))
            return;
        
        // Use board-specific blocks
        if (board == null || board.GameBlocks == null)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Board {0} GameBlocks array not initialized for block ({1},{2})", 
                board != null ? board.BoardIndex : -1, gridX, gridY));
            return;
        }
        
        RigidBodyComponent rigidBody = board.GameBlocks[gridX, gridY].RigidBody;
        if (rigidBody != null)
        {
            // Debug logging to trace collision changes
            // Log.Write(string.Format("TetrisGame: Setting collision for block ({0},{1}) - enableCollision: {2}, players: {3}", 
            //     gridX, gridY, enableCollision, allPlayers.Count));
            
            // Update collision for all players in the scene
            foreach (var player in allPlayers)
            {
                if (player != null && player.IsValid)
                {
                    // true = ignore collision (pass through), false = enable collision (solid)
                    player.IgnoreCollisionWith(rigidBody, !enableCollision);
                }
            }
        }
        else
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: No RigidBodyComponent found for block ({0},{1})", gridX, gridY));
        }
    }
    
    
    #endregion
    
    #region Preview Visual Control Methods
    
    public void SetPreviewBlockHidden(TetrisGameBoard board, int gridX, int gridY)
    {
        if (gridX < 0 || gridX >= PREVIEW_WIDTH || gridY < 0 || gridY >= PREVIEW_HEIGHT)
            return;
        
        MeshComponent mesh = board.PreviewBlocks[gridX, gridY].Mesh;
        if (mesh != null && mesh.IsScriptable)
        {
            try
            {
                mesh.SetIsVisible(false);
            }
            catch (Exception ex)
            {
                Log.Write(LogLevel.Error, string.Format("TetrisGame: Error hiding preview block ({0}, {1}) on Board {2}: {3}", 
                    gridX, gridY, board.BoardIndex, ex.Message));
            }
        }
    }
    
    public void SetPreviewBlockVisible(TetrisGameBoard board, int gridX, int gridY, int pieceType)
    {
        if (gridX < 0 || gridX >= PREVIEW_WIDTH || gridY < 0 || gridY >= PREVIEW_HEIGHT)
            return;
        
        if (!PieceColors.ContainsKey(pieceType))
            return;
        
        MeshComponent mesh = board.PreviewBlocks[gridX, gridY].Mesh;
        if (mesh != null && mesh.IsScriptable)
        {
            try
            {
                // First make the block visible
                mesh.SetIsVisible(true);
                
                // Then set its color
                if (EnableMultiMaterial)
                {
                    // Use new multi-material system (preview blocks never active)
                    SetGameBlockMultiMaterial(mesh, pieceType, false);
                }
                else
                {
                    // Fallback to single-material system
                    Sansar.Color blockColor = PieceColors[pieceType];
                    
                    foreach (var material in mesh.GetRenderMaterials())
                    {
                        var props = material.GetProperties();
                        
                        if (material.HasTint)
                        {
                            props.Tint = blockColor;
                        }
                        
                        if (material.HasEmissiveIntensity)
                        {
                            props.EmissiveIntensity = 0.0f; // No glow for preview
                        }
                        
                        material.SetProperties(props);
                    }
                }
            }
            catch (Exception ex)
            {
                Log.Write(LogLevel.Error, string.Format("TetrisGame: Error showing preview block ({0}, {1}): {2}", gridX, gridY, ex.Message));
            }
        }
    }
    
    public void SetPreviewBackgroundBlockVisible(TetrisGameBoard board, int gridX, int gridY)
    {
        if (gridX < 0 || gridX >= PREVIEW_WIDTH || gridY < 0 || gridY >= PREVIEW_HEIGHT)
            return;
        
        MeshComponent mesh = board.PreviewBackgroundBlocks[gridX, gridY].Mesh;
        if (mesh != null && mesh.IsScriptable)
        {
            try
            {
                // Make the background block visible with black color
                mesh.SetIsVisible(true);
                
                foreach (var material in mesh.GetRenderMaterials())
                {
                    var props = material.GetProperties();
                    
                    if (material.HasTint)
                    {
                        props.Tint = BackgroundColor;
                    }
                    
                    if (material.HasEmissiveIntensity)
                    {
                        props.EmissiveIntensity = 0.0f; // No glow for background
                    }
                    
                    material.SetProperties(props);
                }
            }
            catch (Exception ex)
            {
                Log.Write(LogLevel.Error, string.Format("TetrisGame: Error showing preview background block ({0}, {1}): {2}", gridX, gridY, ex.Message));
            }
        }
    }
    
    public void ClearPreview(TetrisGameBoard board)
    {
        // Hide all preview blocks
        for (int x = 0; x < PREVIEW_WIDTH; x++)
        {
            for (int y = 0; y < PREVIEW_HEIGHT; y++)
            {
                SetPreviewBlockHidden(board, x, y);
            }
        }
    }
    
    public void ShowNextPiece(TetrisGameBoard board, PieceType pieceType)
    {
        // Clear current preview
        ClearPreview(board);
        
        // Get piece shape for rotation 0 (default orientation)
        Vector[] relativePattern = PieceShapes[pieceType][0];
        
        // Calculate center position for 4x4 preview area
        Vector centerOffset = new Vector(1.5f, 1.5f, 0); // Center of 4x4 grid
        
        // Show each block of the piece
        foreach (var blockOffset in relativePattern)
        {
            int x = (int)(centerOffset.X + blockOffset.X);
            int y = (int)(centerOffset.Y + blockOffset.Y);
            
            // Make sure block is within preview bounds
            if (x >= 0 && x < PREVIEW_WIDTH && y >= 0 && y < PREVIEW_HEIGHT)
            {
                SetPreviewBlockVisible(board, x, y, (int)pieceType);
            }
        }
        
        Log.Write(string.Format("TetrisGame: Updated preview to show {0} piece on Board {1}", pieceType, board.BoardIndex));
    }
    
    #endregion
    
    #region Rainbow Debug Mode
    
    private void RainbowDebugSequence(TetrisGameBoard board)
    {
        Log.Write(string.Format("TetrisGame: Rainbow debug sequence starting on Board {0} - cycling through all colors and effects", board.BoardIndex));
        
        // Phase 1: Show all blocks visible in different colors
        for (int colorCycle = 0; colorCycle < 7; colorCycle++) // 7 Tetris piece types
        {
            Log.Write(string.Format("TetrisGame: Rainbow cycle {0} - setting all blocks to color {1} on Board {2}", colorCycle + 1, colorCycle, board.BoardIndex));
            
            for (int x = 0; x < GRID_WIDTH; x++)
            {
                for (int y = 0; y < GRID_HEIGHT; y++)
                {
                    SetBlockVisibility(board, x, y, true);
                    SetBlockColorAndEmissive(board, x, y, colorCycle, 0.0f);
                }
            }
            
            Wait(TimeSpan.FromSeconds(1.0)); // Hold each color for 1 second
        }
        
        // Phase 2: Reset to hidden state
        Log.Write(string.Format("TetrisGame: Rainbow debug complete - resetting all blocks to hidden on Board {0}", board.BoardIndex));
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                SetBlockVisibility(board, x, y, false);
            }
        }
        
        Log.Write(string.Format("TetrisGame: Rainbow debug sequence completed on Board {0} - material control testing finished", board.BoardIndex));
    }
    
    #endregion
    
    #region Grid State and Collision Detection
    
    public bool IsBlockOccupied(TetrisGameBoard board, int x, int y)
    {
        // A block is occupied if it is visible AND is not part of the active piece
        return board.GameBlocks[x, y].IsVisible && !board.GameBlocks[x, y].IsActivePiece;
    }

    public bool IsBlockInBounds(int x, int y)
    {
        return (x >= 0 && x < GRID_WIDTH && y >= 0 && y < GRID_HEIGHT);
    }
        
    public void ClearBlock(TetrisGameBoard board, int x, int y)
    {
        if (IsBlockInBounds(x, y))
        {
            // Update block state
            board.GameBlocks[x, y].IsVisible = false;
            board.GameBlocks[x, y].IsActivePiece = false;
            board.GameBlocks[x, y].FlashMode = FlashType.None;
            
            // Use direct material control
            SetBlockVisibility(board, x, y, false);
        }
    }
    
    public void ClearGrid(TetrisGameBoard board)
    {
        if (!board.GridInitialized)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Board {0} grid not initialized yet - cannot clear", board.BoardIndex));
            return;
        }
        
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                ClearBlock(board, x, y);
            }
        }
        
        Log.Write(string.Format("TetrisGame: Grid cleared for new game on Board {0}", board.BoardIndex));
    }
        
    #endregion

    #region SRS Wall Kick Tables
    
    // SRS wall kick offset data for JLSTZ pieces
    // Format: [fromRotation][toRotation][kickAttempt]
    private static readonly Vector[][][] WallKicksJLSTZ = new Vector[][][]
    {
        // From rotation 0
        new Vector[][]
        {
            // 0->1 (0->R)
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(-1, 1, 0), new Vector(0, -2, 0), new Vector(-1, -2, 0) },
            null, // 0->2 not used
            // 0->3 (0->L)
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(1, 1, 0), new Vector(0, -2, 0), new Vector(1, -2, 0) }
        },
        // From rotation 1 (R)
        new Vector[][]
        {
            // 1->0 (R->0)
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(1, -1, 0), new Vector(0, 2, 0), new Vector(1, 2, 0) },
            // 1->2 (R->2)
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(1, -1, 0), new Vector(0, 2, 0), new Vector(1, 2, 0) },
            null // 1->3 not used
        },
        // From rotation 2
        new Vector[][]
        {
            null, // 2->0 not used
            // 2->1 (2->R)
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(-1, 1, 0), new Vector(0, -2, 0), new Vector(-1, -2, 0) },
            // 2->3 (2->L)
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(1, 1, 0), new Vector(0, -2, 0), new Vector(1, -2, 0) }
        },
        // From rotation 3 (L)
        new Vector[][]
        {
            // 3->0 (L->0)
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(-1, -1, 0), new Vector(0, 2, 0), new Vector(-1, 2, 0) },
            null, // 3->1 not used
            // 3->2 (L->2)
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(-1, -1, 0), new Vector(0, 2, 0), new Vector(-1, 2, 0) }
        }
    };
    
    // SRS wall kick offset data for I piece
    private static readonly Vector[][][] WallKicksI = new Vector[][][]
    {
        // From rotation 0
        new Vector[][]
        {
            // 0->1 (0->R) - Modified to reduce large left jump
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(-1, -1, 0), new Vector(1, 2, 0) },
            null, // 0->2 not used
            // 0->3 (0->L)
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(2, 0, 0), new Vector(-1, 2, 0), new Vector(2, -1, 0) }
        },
        // From rotation 1 (R)
        new Vector[][]
        {
            // 1->0 (R->0) - Modified to match updated 0->1 rotation
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(-1, 0, 0), new Vector(1, 1, 0), new Vector(-1, -2, 0) },
            // 1->2 (R->2)
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(2, 0, 0), new Vector(-1, 2, 0), new Vector(2, -1, 0) },
            null // 1->3 not used
        },
        // From rotation 2
        new Vector[][]
        {
            null, // 2->0 not used
            // 2->1 (2->R)
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(-2, 0, 0), new Vector(1, -2, 0), new Vector(-2, 1, 0) },
            // 2->3 (2->L) - Modified to match updated rotation pattern
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(-1, 0, 0), new Vector(1, 1, 0), new Vector(-1, -2, 0) }
        },
        // From rotation 3 (L)
        new Vector[][]
        {
            // 3->0 (L->0)
            new Vector[] { new Vector(0, 0, 0), new Vector(1, 0, 0), new Vector(-2, 0, 0), new Vector(1, -2, 0), new Vector(-2, 1, 0) },
            null, // 3->1 not used
            // 3->2 (L->2) - Modified to reduce large left jump
            new Vector[] { new Vector(0, 0, 0), new Vector(-1, 0, 0), new Vector(1, 0, 0), new Vector(-1, -1, 0), new Vector(1, 2, 0) }
        }
    };
    
    #endregion
    
    #region Piece Logic and Collision Detection
    
    // Get the block positions for a piece at a specific position and rotation
    private Vector[] GetPieceBlocks(PieceType pieceType, int rotation, Vector centerPosition)
    {
        rotation = rotation % 4;
        if (rotation < 0) rotation += 4;
        
        Vector[] relativePattern = PieceShapes[pieceType][rotation];
        Vector[] absolutePositions = new Vector[relativePattern.Length];
        
        for (int i = 0; i < relativePattern.Length; i++)
        {
            absolutePositions[i] = new Vector(
                centerPosition.X + relativePattern[i].X,
                centerPosition.Y + relativePattern[i].Y,
                centerPosition.Z + relativePattern[i].Z
            );
        }
        
        return absolutePositions;
    }
    
    // FIXED COLLISION DETECTION: Now has access to grid state!
    public bool CanPlacePiece(TetrisGameBoard board, PieceType pieceType, int rotation, Vector position)
    {
        Vector[] blockPositions = GetPieceBlocks(pieceType, rotation, position);
        
        foreach (var blockPos in blockPositions)
        {
            int x = (int)blockPos.X;
            int y = (int)blockPos.Y;

            // Check if position is in bounds first, then check occupation
            if (!IsBlockInBounds(x, y) || IsBlockOccupied(board, x, y))
            {
                return false;
            }
        }
        
        return true;
    }
    
    public bool TryRotatePiece(TetrisGameBoard board, PieceType pieceType, Vector currentPosition, int currentRotation, int direction, out int newRotation, out Vector kickOffset)
    {
        // Calculate target rotation
        newRotation = currentRotation + direction;
        if (newRotation < 0) newRotation = 3;
        if (newRotation > 3) newRotation = 0;
        
        kickOffset = new Vector(0, 0, 0);
        
        // O-piece doesn't need kicks (it's symmetrical)
        if (pieceType == PieceType.O)
        {
            return true;
        }
        
        // Get appropriate kick table
        Vector[][][] kickTable = (pieceType == PieceType.I) ? WallKicksI : WallKicksJLSTZ;
        
        // Calculate the kick table index
        // For clockwise: use direct mapping
        // For counter-clockwise: map the transitions correctly
        int toIndex = -1;
        if (direction > 0) // Clockwise
        {
            switch (currentRotation)
            {
                case 0: toIndex = 0; break; // 0->1
                case 1: toIndex = 1; break; // 1->2
                case 2: toIndex = 2; break; // 2->3
                case 3: toIndex = 0; break; // 3->0
            }
        }
        else // Counter-clockwise
        {
            switch (currentRotation)
            {
                case 0: toIndex = 2; break; // 0->3
                case 1: toIndex = 0; break; // 1->0
                case 2: toIndex = 1; break; // 2->1
                case 3: toIndex = 2; break; // 3->2
            }
        }
        
        // Get the kicks for this rotation transition
        Vector[] kicks = kickTable[currentRotation][toIndex];
        if (kicks == null)
        {
            // This shouldn't happen with proper tables, but just in case
            newRotation = currentRotation;
            return false;
        }
        
        // Try each kick offset
        foreach (var kick in kicks)
        {
            Vector testPosition = new Vector(
                currentPosition.X + kick.X,
                currentPosition.Y + kick.Y,
                currentPosition.Z + kick.Z
            );
            
            if (CanPlacePiece(board, pieceType, newRotation, testPosition))
            {
                kickOffset = kick;
                return true;
            }
        }
        
        // No valid position found
        newRotation = currentRotation;
        return false;
    }
    
    #endregion
    
    #region Game Logic and Flow
    
    /// <summary>
    /// Clear the visual grid for a specific board
    /// </summary>
    private void ClearGridForBoard(TetrisGameBoard board)
    {
        // TODO: Implement board-specific grid clearing
        // This should hide all visible blocks on the specified board
        Log.Write(string.Format("TetrisGame: ClearGridForBoard called for Board {0} (placeholder)", board.BoardIndex));
    }
    
    /// <summary>
    /// Spawn a new piece on a specific board
    /// </summary>
    private void SpawnNewPieceForBoard(TetrisGameBoard board)
    {
        Log.Write(string.Format("TetrisGame: SpawnNewPieceForBoard - Starting for Board {0}", board.BoardIndex));
        
        // Use next piece as current piece, or generate random if first spawn
        if (board.NextPieceInitialized)
        {
            board.CurrentPieceType = board.NextPieceType;
            Log.Write(string.Format("TetrisGame: SpawnNewPieceForBoard - Using next piece type {0} for Board {1}", board.CurrentPieceType, board.BoardIndex));
        }
        else
        {
            // First piece - generate random
            PieceType[] pieceTypes = { PieceType.I, PieceType.O, PieceType.T, PieceType.S, PieceType.Z, PieceType.J, PieceType.L };
            board.CurrentPieceType = pieceTypes[randomGenerator.Next(pieceTypes.Length)];
            board.NextPieceInitialized = true; // Mark system as initialized for this board
            Log.Write(string.Format("TetrisGame: SpawnNewPieceForBoard - First piece for Board {0}, selected random type {1}", board.BoardIndex, board.CurrentPieceType));
        }
        
        // Generate next piece and update preview for this board
        board.GenerateNextPiece(this);
        
        board.CurrentPieceRotation = 0;
        
        // Get spawn position (center top with safety margin for piece extensions)
        board.CurrentPiecePosition = new Vector(5, 18, 0);
        
        Log.Write(string.Format("TetrisGame: SpawnNewPieceForBoard - About to call CanPlacePiece for Board {0}", board.BoardIndex));
        
        // Debug: Check if the spawn area is clear
        Log.Write(string.Format("TetrisGame: Checking spawn area (Y=16-19) before spawning on Board {0}...", board.BoardIndex));
        int occupiedCount = 0;
        for (int y = GRID_HEIGHT - 4; y < GRID_HEIGHT; y++)
        {
            for (int x = 0; x < GRID_WIDTH; x++)
            {
                if (IsBlockOccupied(board, x, y))
                {
                    Log.Write(string.Format("TetrisGame: WARNING - Found occupied block in spawn area at ({0}, {1}) on Board {2}", x, y, board.BoardIndex));
                    occupiedCount++;
                }
            }
        }

        if (occupiedCount == 0)
        {
            Log.Write(string.Format("TetrisGame: Spawn area is clear - safe to spawn on Board {0}", board.BoardIndex));
        }
        else
        {
            Log.Write(string.Format("TetrisGame: WARNING - {0} occupied blocks found in spawn area on Board {1}", occupiedCount, board.BoardIndex));
        }
        
        // PROPER GAME OVER CHECK: Can the new piece spawn without collision?
        if (!CanPlacePiece(board, board.CurrentPieceType, board.CurrentPieceRotation, board.CurrentPiecePosition))
        {
            Log.Write(string.Format("TetrisGame: GAME OVER - New piece collides with existing locked pieces on Board {0}", board.BoardIndex));
            // Debug: Log why we can't spawn
            Log.Write(string.Format("TetrisGame: Cannot spawn piece {0} at {1} on Board {2} - collision detected", board.CurrentPieceType, board.CurrentPiecePosition, board.BoardIndex));
            Vector[] testBlocks = GetPieceBlocks(board.CurrentPieceType, board.CurrentPieceRotation, board.CurrentPiecePosition);
            foreach (var blockPos in testBlocks)
            {
                int x = (int)blockPos.X;
                int y = (int)blockPos.Y;
                Log.Write(string.Format("TetrisGame: Block at ({0}, {1}) - InBounds: {2}, Occupied: {3}", x, y, IsBlockInBounds(x, y), IsBlockOccupied(board, x, y)));
            }
            
            // FORCE spawn the piece anyway for visual feedback
            Log.Write(string.Format("TetrisGame: Force spawning final piece for game over visualization on Board {0}", board.BoardIndex));
            board.CurrentPieceBlocks = new List<Vector>(GetPieceBlocks(board.CurrentPieceType, board.CurrentPieceRotation, board.CurrentPiecePosition));
            
            // Game over - with final piece visible
            board.EndGame(this);
            return;
        }
        
        Log.Write(string.Format("TetrisGame: SpawnNewPieceForBoard - CanPlacePiece returned true for Board {0}", board.BoardIndex));
        // Calculate block positions
        board.CurrentPieceBlocks = new List<Vector>(GetPieceBlocks(board.CurrentPieceType, board.CurrentPieceRotation, board.CurrentPiecePosition));
        Log.Write(string.Format("TetrisGame: Successfully validated spawn position for {0} at {1} on Board {2}", board.CurrentPieceType, board.CurrentPiecePosition, board.BoardIndex));

        // Update ghost piece for the new piece
        UpdateGhostPiece(board);

        // Active piece display now handled by visual update cycle
        Log.Write(string.Format("TetrisGame: Spawned new piece: {0} at {1} with {2} blocks on Board {3}", board.CurrentPieceType, board.CurrentPiecePosition, board.CurrentPieceBlocks.Count, board.BoardIndex));
        
        // Initialize drop timer to prevent immediate drop
        board.LastDropTime = Stopwatch.GetTimestamp();
    }
    
    
    
    private void GameLoop()
    {
        while (true)
        {
            // Process each board independently
            foreach (var board in gameBoards)
            {
                if (board == null || !board.GridInitialized) continue;
                
                // Process based on board's current state
                switch (board.CurrentState)
                {
                    case GameState.Playing:
                        ProcessPlayingState(board);
                        break;
                        
                    case GameState.ClearingLines:
                        ProcessLineClearingState(board);
                        break;
                        
                    case GameState.GameOver:
                        // Visual sync only - no game logic processing needed
                        break;
                }
                
                // Visual sync for this board
                if (board.CurrentState != GameState.WaitingForPlayer)
                {
                    SyncAllVisualsToCompleteState(board);
                }
            }
            
            // Check for timed mode expiration
            if (currentGameMode == GameMode.Timed && timedModeActive)
            {
                double elapsedSeconds = (double)(Stopwatch.GetTimestamp() - timedModeStartTime) / Stopwatch.Frequency;
                double remainingSeconds = timedModeSeconds - elapsedSeconds;
                
                // Announce time warnings
                if (remainingSeconds <= 30 && remainingSeconds > 29.9)
                {
                    SendChatToAll("30 seconds remaining!");
                }
                else if (remainingSeconds <= 10 && remainingSeconds > 9.9)
                {
                    SendChatToAll("10 seconds remaining!");
                }
                else if (remainingSeconds <= 0)
                {
                    EndTimedMode();
                }
            }
            
            Wait(TimeSpan.FromSeconds(0.1)); // 10 FPS
        }
    }
    
    private void ProcessBoardSpawnQueue()
    {
        while (true)
        {
            if (!isSpawningBoard && boardSpawnQueue.Count > 0)
            {
                isSpawningBoard = true;
                TetrisGameBoard board = boardSpawnQueue.Dequeue();
                
                Log.Write(string.Format("TetrisGame: Starting block spawn for Board {0} ({1} boards remaining in queue)", 
                    board.BoardIndex, boardSpawnQueue.Count));
                
                // Spawn all blocks for this board
                SpawnBlocksForBoard(board);
                
                // Mark spawning complete
                isSpawningBoard = false;
                
                // Small delay between boards to ensure clean separation
                if (boardSpawnQueue.Count > 0)
                {
                    Wait(TimeSpan.FromSeconds(0.5));
                }
            }
            
            Wait(TimeSpan.FromSeconds(0.1)); // Check queue 10 times per second
        }
    }
    
    private void ProcessPlayingState(TetrisGameBoard board)
    {
        if (board.CurrentPieceBlocks != null && !board.IsHardDropping)
        {
            // Auto-drop
            double elapsedSeconds = (double)(Stopwatch.GetTimestamp() - board.LastDropTime) / Stopwatch.Frequency;
            if (elapsedSeconds >= board.DropInterval)
            {
                if (!MovePieceDown(board))
                {
                    Log.Write(string.Format("TetrisGame: Board {0} - Piece cannot move down, calling OnPieceLanded", board.BoardIndex));
                    OnPieceLanded(board);
                }
                board.LastDropTime = Stopwatch.GetTimestamp();
            }
            
            // Key repeat
            board.HandleKeyRepeat(this);
        }
        
        // Logic-derived active flags must be set before visuals
        UpdateActivePieceFlags(board);
    }
    
    private void ProcessLineClearingState(TetrisGameBoard board)
    {
        // Check if line clearing animation is complete
        double elapsedTime = (double)(Stopwatch.GetTimestamp() - board.LineClearStartTime) / Stopwatch.Frequency;
        if (elapsedTime >= LINE_CLEAR_DURATION)
        {
            Log.Write(string.Format("TetrisGame: Board {0} - Line clearing animation complete, dropping lines", board.BoardIndex));
            
            // Clear and drop the lines
            if (board.LinesToClear != null && board.LinesToClear.Count > 0)
            {
                ClearAndDropLines(board, board.LinesToClear);
                board.LinesToClear = null;
            }
            
            // Check for cascade line clears after dropping
            int additionalLinesCleared = CheckAndClearLines(board);
            if (additionalLinesCleared > 0)
            {
                // More lines formed after dropping - continue clearing animation
                Log.Write(string.Format("TetrisGame: Board {0} - Cascade line clear detected - {1} additional lines", board.BoardIndex, additionalLinesCleared));
                // LineClearStartTime and LinesToClear already set by CheckAndClearLines
                // Stay in ClearingLines state to continue the cycle
            }
            else
            {
                // No more lines to clear - transition back to playing state and spawn new piece
                board.CurrentState = GameState.Playing;
                SpawnNewPiece(board);
                Log.Write(string.Format("TetrisGame: Board {0} - Returned to Playing state, new piece spawned", board.BoardIndex));
            }
        }
    }
    
    private void SpawnNewPiece(TetrisGameBoard board)
    {
        Log.Write(string.Format("TetrisGame: Board {0} - SpawnNewPiece - Starting", board.BoardIndex));
        
        // Use next piece as current piece, or generate random if first spawn
        if (board.NextPieceInitialized)
        {
            board.CurrentPieceType = board.NextPieceType;
            Log.Write(string.Format("TetrisGame: SpawnNewPiece - Using next piece type {0}", board.CurrentPieceType));
        }
        else
        {
            // First piece - generate random
            PieceType[] pieceTypes = { PieceType.I, PieceType.O, PieceType.T, PieceType.S, PieceType.Z, PieceType.J, PieceType.L };
            board.CurrentPieceType = pieceTypes[randomGenerator.Next(pieceTypes.Length)];
            board.NextPieceInitialized = true; // Mark system as initialized
            Log.Write(string.Format("TetrisGame: SpawnNewPiece - First piece, selected random type {0}", board.CurrentPieceType));
        }
        
        // Generate next piece and update preview
        board.GenerateNextPiece(this);
        
        board.CurrentPieceRotation = 0;
        
        // Get spawn position (center top with safety margin for piece extensions)
        board.CurrentPiecePosition = new Vector(5, 18, 0);
        
        Log.Write("TetrisGame: SpawnNewPiece - About to call CanPlacePiece");
        
        // Debug: Check if the spawn area is clear
        Log.Write("TetrisGame: Checking spawn area (Y=16-19) before spawning...");
        int occupiedCount = 0;
        for (int y = GRID_HEIGHT - 4; y < GRID_HEIGHT; y++)
        {
            for (int x = 0; x < GRID_WIDTH; x++)
            {
                if (IsBlockOccupied(board, x, y))
                {
                    Log.Write(string.Format("TetrisGame: WARNING - Found occupied block in spawn area at ({0}, {1})", x, y));
                    occupiedCount++;
                }
            }
        }

        if (occupiedCount == 0)
        {
            Log.Write("TetrisGame: Spawn area is clear - safe to spawn");
        }
        else
        {
            Log.Write(string.Format("TetrisGame: WARNING - {0} occupied blocks found in spawn area", occupiedCount));
        }
        
        // PROPER GAME OVER CHECK: Can the new piece spawn without collision?
        if (!CanPlacePiece(board, board.CurrentPieceType, board.CurrentPieceRotation, board.CurrentPiecePosition))
        {
            Log.Write("TetrisGame: GAME OVER - New piece collides with existing locked pieces");
            // Debug: Log why we can't spawn
            Log.Write(string.Format("TetrisGame: Cannot spawn piece {0} at {1} - collision detected", board.CurrentPieceType, board.CurrentPiecePosition));
            Vector[] testBlocks = GetPieceBlocks(board.CurrentPieceType, board.CurrentPieceRotation, board.CurrentPiecePosition);
            foreach (var blockPos in testBlocks)
            {
                int x = (int)blockPos.X;
                int y = (int)blockPos.Y;
                Log.Write(string.Format("TetrisGame: Block at ({0}, {1}) - InBounds: {2}, Occupied: {3}", x, y, IsBlockInBounds(x, y), IsBlockOccupied(board, x, y)));
            }
            
            // FORCE spawn the piece anyway for visual feedback
            Log.Write("TetrisGame: Force spawning final piece for game over visualization");
            board.CurrentPieceBlocks = new List<Vector>(GetPieceBlocks(board.CurrentPieceType, board.CurrentPieceRotation, board.CurrentPiecePosition));
            
            // Game over - with final piece visible
            board.EndGame(this);
            return;
        }
        
        Log.Write("TetrisGame: SpawnNewPiece - CanPlacePiece returned true");
        
        // Calculate block positions
        board.CurrentPieceBlocks = new List<Vector>(GetPieceBlocks(board.CurrentPieceType, board.CurrentPieceRotation, board.CurrentPiecePosition));
        
        Log.Write(string.Format("TetrisGame: Successfully validated spawn position for {0} at {1}", board.CurrentPieceType, board.CurrentPiecePosition));
        
        // Active piece display now handled by visual update cycle
        
        Log.Write(string.Format("TetrisGame: Spawned new piece: {0} at {1} with {2} blocks", board.CurrentPieceType, board.CurrentPiecePosition, board.CurrentPieceBlocks.Count));
        
        // Initialize drop timer to prevent immediate drop
        board.LastDropTime = Stopwatch.GetTimestamp();
    }
           
    
    #endregion
    
    #region Piece Movement
    
    public bool MovePieceLeft(TetrisGameBoard board)
    {
        Vector newPosition = new Vector(board.CurrentPiecePosition.X - 1, board.CurrentPiecePosition.Y, 0);
        bool moved = TryMovePieceForBoard(board, newPosition, board.CurrentPieceRotation);
        
        // Play move sound if piece successfully moved
        if (moved)
        {
            PlayMoveSound(GetBoardChairPosition(board));
        }
        
        return moved;
    }
    
    public bool MovePieceRight(TetrisGameBoard board)
    {
        Vector newPosition = new Vector(board.CurrentPiecePosition.X + 1, board.CurrentPiecePosition.Y, 0);
        bool moved = TryMovePieceForBoard(board, newPosition, board.CurrentPieceRotation);
        
        // Play move sound if piece successfully moved
        if (moved)
        {
            PlayMoveSound(GetBoardChairPosition(board));
        }
        
        return moved;
    }
    
    public bool MovePieceDown(TetrisGameBoard board, bool triggeredByKey = false)
    {
        Vector newPosition = new Vector(board.CurrentPiecePosition.X, board.CurrentPiecePosition.Y - 1, 0);
        bool moved = TryMovePieceForBoard(board, newPosition, board.CurrentPieceRotation);
        
        // Play move sound only if piece successfully moved AND it was triggered by a key press
        if (moved && triggeredByKey)
        {
            PlayMoveSound(GetBoardChairPosition(board));
        }
        
        return moved;
    }
    
    private bool TryMovePieceForBoard(TetrisGameBoard board, Vector newPosition, int newRotation)
    {
        Vector oldPosition = board.CurrentPiecePosition;
        
        // Check collision with locked pieces on this board
        if (!CanPlacePiece(board, board.CurrentPieceType, newRotation, newPosition))
        {
            Log.Write(string.Format("TetrisGame: TryMovePieceForBoard - CanPlacePiece returned false for new position {0} and rotation {1}", newPosition, newRotation));
            return false; // Cannot move to new position
        }
        
        // Update board's piece state
        board.CurrentPiecePosition = newPosition;
        board.CurrentPieceRotation = newRotation;
        board.CurrentPieceBlocks = new List<Vector>(GetPieceBlocks(board.CurrentPieceType, newRotation, newPosition));
        
        // Update ghost piece for new position
        UpdateGhostPiece(board);
        
        // Log.Write(string.Format("TetrisGame: TryMovePieceForBoard SUCCESS - moved from {0} to {1}, rotation {2}", oldPosition, newPosition, newRotation));
        return true;
    }
        
    public void SlowDownPiece(TetrisGameBoard board)
    {
        // Temporarily increase drop interval to slow down the piece
        board.DropInterval = Math.Min(board.DropInterval * 1.5f, 2.0f); // Cap at 2 seconds
        Log.Write(string.Format("TetrisGame: Slowed down drop interval to {0} seconds", board.DropInterval));
    }
    
    public void RotatePiece(TetrisGameBoard board, int direction)
    {
        int newRotation;
        Vector kickOffset;
        
        // Try to rotate with SRS wall kicks
        Vector currentPos = board.CurrentPiecePosition;
        if (TryRotatePiece(board, board.CurrentPieceType, currentPos, board.CurrentPieceRotation, direction, out newRotation, out kickOffset))
        {
            // Apply the kick offset to get the final position
            Vector newPosition = new Vector(
                board.CurrentPiecePosition.X + kickOffset.X,
                board.CurrentPiecePosition.Y + kickOffset.Y,
                0
            );
            
            // Update the piece state
            bool moved = TryMovePieceForBoard(board, newPosition, newRotation);
            
            if (moved)
            {
                // Play rotate sound if piece successfully rotated
                PlayRotateSound(GetBoardChairPosition(board));
                
                // Log the kick if it wasn't a basic rotation
                if (kickOffset.X != 0 || kickOffset.Y != 0)
                {
                    Log.Write(string.Format("TetrisGame: Rotation succeeded with wall kick ({0}, {1})", kickOffset.X, kickOffset.Y));
                }
            }
        }
    }
    
    public void HardDrop(TetrisGameBoard board)
    {
        Log.Write(string.Format("TetrisGame: HardDrop starting - piece at ({0}, {1})", board.CurrentPiecePosition.X, board.CurrentPiecePosition.Y));
        board.IsHardDropping = true;
        
        // Drop piece as far down as possible
        int dropCount = 0;
        while (MovePieceDown(board))
        {
            dropCount++;
            Log.Write(string.Format("TetrisGame: HardDrop step {0} - piece now at ({1}, {2})", dropCount, board.CurrentPiecePosition.X, board.CurrentPiecePosition.Y));
            // Keep dropping until it can't move down
        }
        
        Log.Write(string.Format("TetrisGame: HardDrop finished after {0} steps - final position ({1}, {2})", dropCount, board.CurrentPiecePosition.X, board.CurrentPiecePosition.Y));
        board.IsHardDropping = false;
        
        // Play hard drop sound if piece dropped at least one position
        if (dropCount > 0)
        {
            PlayHardDropSound(GetBoardChairPosition(board));
        }
        
        // Update visual state to clear old position and show final position
        UpdateActivePieceFlags(board);
        
        // Piece has landed
        OnPieceLanded(board);
    }
    
    private void UpdateGhostPiece(TetrisGameBoard board)
    {
        // Clear ghost if disabled or no current piece
        if (!EnableGhostPiece || !board.GhostPieceEnabled || board.CurrentPieceBlocks == null)
        {
            board.GhostPieceBlocks = null;
            return;
        }
        
        // Start from current position and move down until collision
        Vector testPos = board.CurrentPiecePosition;
        Vector lastValidPos = testPos;
        
        // Keep moving down until we can't
        while (true)
        {
            Vector nextPos = new Vector(testPos.X, testPos.Y - 1, 0);
            if (CanPlacePiece(board, board.CurrentPieceType, board.CurrentPieceRotation, nextPos))
            {
                testPos = nextPos;
            }
            else
            {
                break;
            }
        }
        
        // Don't show ghost if it's at the same position as the current piece
        if (testPos.Y == board.CurrentPiecePosition.Y)
        {
            board.GhostPieceBlocks = null;
            return;
        }
        
        // Update ghost position and blocks
        board.GhostPiecePosition = testPos;
        board.GhostPieceBlocks = new List<Vector>(GetPieceBlocks(
            board.CurrentPieceType, 
            board.CurrentPieceRotation, 
            testPos
        ));
    }
    
    private void OnPieceLanded(TetrisGameBoard board)
    {
        if (board.CurrentPieceBlocks == null) return;
    
        Log.Write("TetrisGame: Piece landed - locking into grid state");
    
        // Commit locked blocks
        foreach (var blockPos in board.CurrentPieceBlocks)
        {
            int x = (int)blockPos.X;
            int y = (int)blockPos.Y;
            if (IsBlockInBounds(x, y))
            {
                TetrisGameBlock block = board.GameBlocks[x, y];
                block.PieceType = (int)board.CurrentPieceType;
                block.IsVisible = true;
                block.IsActivePiece = false; // no longer active
                board.GameBlocks[x, y] = block;
            }
        }
    
        // Play lock piece sound
        PlayLockPieceSound(GetBoardChairPosition(board));
    
        // Clear active piece and ghost
        board.CurrentPieceBlocks = null;
        board.GhostPieceBlocks = null;
    
        // Check for completed lines (no waits; flash timed by FlashStartTime)
        Log.Write(string.Format("TetrisGame: Board {0} - Starting line clearing check", board.BoardIndex));
        int cleared = CheckAndClearLines(board);
        Log.Write(string.Format("TetrisGame: Board {0} - Line clearing complete - {1} lines cleared", board.BoardIndex, cleared));
    
        if (cleared > 0)
        {
            board.UpdateScore(this, cleared);
            // Transition to line clearing state - new piece will spawn after lines are cleared
            board.CurrentState = GameState.ClearingLines;
            Log.Write("TetrisGame: Transitioning to ClearingLines state");
        }
        else
        {
            // No lines to clear - spawn next piece immediately
            SpawnNewPiece(board);
        }
        
        Log.Write("TetrisGame: OnPieceLanded complete");
    }
    
    #endregion
    
    #region Line Clearing
    
    public int CheckAndClearLines(TetrisGameBoard board)
    {
        if (!board.GridInitialized)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Board {0} - Grid not initialized yet - cannot clear lines", board.BoardIndex));
            return 0;
        }
        
        List<int> completedLines = new List<int>();
        
        // Check each row from bottom to top
        for (int y = 0; y < GRID_HEIGHT; y++)
        {
            if (IsLineComplete(board, y))
            {
                Log.Write(string.Format("TetrisGame: Board {0} - CheckAndClearLines - Line {1} found to be complete", board.BoardIndex, y));
                completedLines.Add(y);
            }
        }

        // Clear completed lines with visual effect
        if (completedLines.Count > 0)
        {
            // Save lines to clear for later processing
            board.LinesToClear = completedLines;
            board.LineClearStartTime = Stopwatch.GetTimestamp();
            
            // Start the flash animation (no coroutine needed)
            LineClearSequence(board, completedLines);
        }
        
        return completedLines.Count;
    }
    
    private bool IsLineComplete(TetrisGameBoard board, int y)
    {
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            if (!board.GameBlocks[x, y].IsVisible)
                return false;
        }
        return true;
    }
    
    private void LineClearSequence(TetrisGameBoard board, List<int> completedLines)
    {
        Log.Write(string.Format("TetrisGame: Board {0} - LineClearSequence - {1} lines to clear", board.BoardIndex, completedLines.Count));
        // Phase 1: Flash intent only (timed by FlashStartTime)
        FlashCompletedLines(board, completedLines);
    
        // Play line clear sound based on number of lines
        if (completedLines.Count == 4)
        {
            PlayTetrisSound(GetBoardChairPosition(board));
        }
        else if (completedLines.Count > 0)
        {
            PlayLineClearSound(GetBoardChairPosition(board));
        }
    
        // No wait here; visual loop renders flash by time.
        // Phase 2 will be triggered by GameLoop when animation completes
    
        Log.Write(string.Format("TetrisGame: LineClearSequence - Cleared {0} lines", completedLines.Count));
    }
    
    private void FlashCompletedLines(TetrisGameBoard board, List<int> lines)
    {
        // Set flash state for completed lines
        float currentTime = (float)(Stopwatch.GetTimestamp() / (double)Stopwatch.Frequency);
        foreach (int y in lines)
        {
            for (int x = 0; x < GRID_WIDTH; x++)
            {
                board.GameBlocks[x, y].FlashMode = FlashType.LineClear;
                board.GameBlocks[x, y].FlashStartTime = currentTime;
            }
        }
    }
    

    private void ClearAndDropLines(TetrisGameBoard board, List<int> completedLines)
    {
        if (completedLines.Count == 0) return;
        Log.Write(string.Format("TetrisGame: ClearAndDropLines - {0} lines to clear", completedLines.Count));

        // Create a mapping of where each line should move to
        int writeY = 0;

        for (int readY = 0; readY < GRID_HEIGHT; readY++)
        {
            Log.Write(string.Format("TetrisGame: ClearAndDropLines - Checking line {0}", readY));
            if (!completedLines.Contains(readY))
            {
                Log.Write(string.Format("TetrisGame: ClearAndDropLines - Copying line {0} to {1}", readY, writeY));
                // This line survives - copy its state to writeY position
                for (int x = 0; x < GRID_WIDTH; x++)
                {
                    // Copy state data from readY to writeY
                    board.GameBlocks[x, writeY].PieceType = board.GameBlocks[x, readY].PieceType;
                    board.GameBlocks[x, writeY].IsVisible = board.GameBlocks[x, readY].IsVisible;
                    board.GameBlocks[x, writeY].IsActivePiece = board.GameBlocks[x, readY].IsActivePiece;
                    board.GameBlocks[x, writeY].FlashMode = FlashType.None;
                    board.GameBlocks[x, writeY].FlashStartTime = 0f;
                    
                    // Reset visual tracking to force proper updates
                    board.GameBlocks[x, writeY].LastEmissiveIntensity = -1f; // Force update
                    board.GameBlocks[x, writeY].LastColorIndex = -1; // Force update
                }
                writeY++;
            }
        }

        // Clear the top lines that are now empty
        for (int y = writeY; y < GRID_HEIGHT; y++)
        {
            for (int x = 0; x < GRID_WIDTH; x++)
            {
                // Reset all block state to ensure proper visual updates
                board.GameBlocks[x, y].PieceType = 0;  // Empty block
                board.GameBlocks[x, y].IsVisible = false;
                board.GameBlocks[x, y].IsActivePiece = false;
                board.GameBlocks[x, y].FlashMode = FlashType.None;
            }
        }
        
        Log.Write("TetrisGame: ClearAndDropLines - Complete");
    }
    
    /// <summary>
    /// Logic-only: mark current active piece and ghost piece flags without mutating locked PieceType cells.
    /// </summary>
    private void UpdateActivePieceFlags(TetrisGameBoard board)
    {
        // Clear previous active flags and visibility intent for game blocks
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                if (board.GameBlocks[x, y].IsActivePiece)
                {
                    board.GameBlocks[x, y].IsActivePiece = false;
                    board.GameBlocks[x, y].IsVisible = false;
                }
                
                // Hide all ghost blocks - we'll show only the ones we need
                if (board.GhostBlocks[x, y] != null)
                {
                    board.GhostBlocks[x, y].IsVisible = false;
                }
            }
        }

        // Set ghost piece visibility using ghost blocks
        if (board.GhostPieceBlocks != null && EnableGhostPiece && board.GhostPieceEnabled && GhostBlock != null)
        {
            foreach (var p in board.GhostPieceBlocks)
            {
                int x = (int)p.X;
                int y = (int)p.Y;
                if (IsBlockInBounds(x, y) && !IsBlockOccupied(board, x, y) && board.GhostBlocks[x, y] != null)
                {
                    board.GhostBlocks[x, y].IsVisible = true;
                    board.GhostBlocks[x, y].PieceType = (int)board.CurrentPieceType;
                }
            }
        }

        // Then set active piece flags (game blocks)
        if (board.CurrentPieceBlocks != null)
        {
            foreach (var p in board.CurrentPieceBlocks)
            {
                int x = (int)p.X;
                int y = (int)p.Y;
                if (IsBlockInBounds(x, y))
                {
                    board.GameBlocks[x, y].IsActivePiece = true;
                    board.GameBlocks[x, y].IsVisible = true; // visible while falling
                    // Do NOT set b.PieceType here; keep for locked blocks only
                    
                    // Hide ghost block at this position since active piece takes priority
                    if (board.GhostBlocks[x, y] != null)
                    {
                        board.GhostBlocks[x, y].IsVisible = false;
                    }
                }
            }
        }
    }
    
    /// <summary>
    /// Visual update thread - updates ALL block visuals to match complete game state
    /// Runs independently from game logic every 0.1 seconds
    /// </summary>
    private void SyncAllVisualsToCompleteState(TetrisGameBoard board)
    {
        float currentTime = (float)(Stopwatch.GetTimestamp() / (double)Stopwatch.Frequency);

        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                TetrisGameBlock b = board.GameBlocks[x, y];

                // Determine desired visual state including flash effects
                float desiredEmissive = b.IsActivePiece ? 1.0f : 0.0f;
                int pieceTypeToRender = b.IsActivePiece ? (int)board.CurrentPieceType : b.PieceType;
                Sansar.Color desiredColor = PieceColors.ContainsKey(pieceTypeToRender) ? PieceColors[pieceTypeToRender] : Sansar.Color.Black;
                
                // Override for flash effects
                if (b.FlashMode == FlashType.LineClear)
                {
                    float t = b.GetTimeSinceFlashStart(currentTime);
                    if (t > 0.6f)
                    {
                        b.FlashMode = FlashType.None;
                    }
                    else
                    {
                        // Flash white with high emissive
                        desiredColor = Sansar.Color.White;
                        desiredEmissive = 15.0f;
                    }
                }
                else if (b.FlashMode == FlashType.GameOver)
                {
                    float t = b.GetTimeSinceFlashStart(currentTime);
                    // Pulsing effect for game over
                    float pulsePhase = (t * b.FlashRate * 2.0f * 3.14159f);
                    float pulseIntensity = (float)Math.Sin(pulsePhase) * 0.5f + 0.5f;
                    desiredEmissive = 2.0f + (pulseIntensity * 8.0f); // Pulse between 2.0 and 10.0
                }

                // Update visibility if changed
                if (b.IsVisible != b.LastVisible)
                {
                    SetBlockVisibility(board, x, y, b.IsVisible);
                    SetBlockCollision(board, x, y, b.IsVisible);
                    b.LastVisible = b.IsVisible;
                }

                // Update color and emissive through single path
                if ((pieceTypeToRender != b.LastColorIndex) || Math.Abs(desiredEmissive - b.LastEmissiveIntensity) > 0.001f)
                {
                    // Check if we need white flash (for line clear)
                    bool isWhiteFlash = (b.FlashMode == FlashType.LineClear && b.GetTimeSinceFlashStart(currentTime) <= 0.6f);
                    
                    if (isWhiteFlash)
                    {
                        // Special handling for white flash - use board-specific arrays
                        if (board != null && board.GameBlocks != null)
                        {
                            MeshComponent mesh = board.GameBlocks[x, y].Mesh;
                        if (mesh != null)
                        {
                            if (EnableMultiMaterial)
                            {
                                // Use new multi-material system for flash
                                SetGameBlockMultiMaterial(mesh, pieceTypeToRender, false, true); // isFlashing = true
                            }
                            else
                            {
                                // Fallback to single-material system
                                foreach (var material in mesh.GetRenderMaterials())
                                {
                                    var props = material.GetProperties();
                                    if (material.HasTint) props.Tint = Sansar.Color.White;
                                    if (material.HasEmissiveIntensity) props.EmissiveIntensity = desiredEmissive;
                                    material.SetProperties(props);
                                }
                            }
                        }
                        }
                    }
                    else
                    {
                        SetBlockColorAndEmissive(board, x, y, pieceTypeToRender, desiredEmissive);
                    }
                    
                    b.LastColorIndex = pieceTypeToRender;
                    b.LastEmissiveIntensity = desiredEmissive;
                }
                
                board.GameBlocks[x, y] = b;
            }
        }
        
        // Update ghost block visuals if ghost blocks are available
        if (GhostBlock != null && board.GhostBlocks != null)
        {
            for (int x = 0; x < GRID_WIDTH; x++)
            {
                for (int y = 0; y < GRID_HEIGHT; y++)
                {
                    TetrisGameBlock ghost = board.GhostBlocks[x, y];
                    if (ghost != null && ghost.Mesh != null)
                    {
                        // Update visibility
                        if (ghost.IsVisible != ghost.LastVisible)
                        {
                            ghost.Mesh.SetIsVisible(ghost.IsVisible);
                            ghost.LastVisible = ghost.IsVisible;
                        }
                        
                        // Update color if visible and changed
                        if (ghost.IsVisible && ghost.PieceType != ghost.LastColorIndex)
                        {
                            Sansar.Color ghostColor = PieceColors.ContainsKey(ghost.PieceType) ? PieceColors[ghost.PieceType] : Sansar.Color.Black;
                            
                            // Apply transparency to the ghost piece color
                            foreach (var material in ghost.Mesh.GetRenderMaterials())
                            {
                                var props = material.GetProperties();
                                if (material.HasTint) 
                                {
                                    props.Tint = new Sansar.Color(
                                        0.85f,  // Slightly brighter than before since we have dedicated ghost blocks
                                        0.99f,
                                        1.0f,
                                        0.5f  // Alpha for transparency
                                    );
                                }
                                material.SetProperties(props);
                            }
                            
                            ghost.LastColorIndex = ghost.PieceType;
                        }
                    }
                }
            }
        }
    }
    
    
    #endregion
    
    
    #region Game End
    
    private void MarkFinalPieceForGameOverFlash(TetrisGameBoard board)
    {
        Log.Write("TetrisGame: Marking final piece blocks for continuous game over flash");
        
        // Use provided board for flash state
        if (board == null || board.GameBlocks == null)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Board {0} GameBlocks array not initialized for flash marking", 
                board != null ? board.BoardIndex : -1));
            return;
        }
        
        float currentTime = (float)(Stopwatch.GetTimestamp() / (double)Stopwatch.Frequency);
        
        // Clear any existing game over flash state using board-specific arrays
        for (int x = 0; x < GRID_WIDTH; x++)
        {
            for (int y = 0; y < GRID_HEIGHT; y++)
            {
                if (board.GameBlocks[x, y].FlashMode == FlashType.GameOver)
                {
                    TetrisGameBlock block = board.GameBlocks[x, y];
                    block.FlashMode = FlashType.None;
                    board.GameBlocks[x, y] = block;
                }
            }
        }
        
        // Mark the current piece blocks for flashing
        int blocksMarked = 0;
        if (board.CurrentPieceBlocks != null)
        {
            foreach (var pieceBlock in board.CurrentPieceBlocks)
            {
                int x = (int)pieceBlock.X;
                int y = (int)pieceBlock.Y;
                
                if (IsBlockInBounds(x, y))
                {
                    TetrisGameBlock block = board.GameBlocks[x, y];
                    block.PieceType = (int)board.CurrentPieceType; // Ensure piece type is set
                    block.IsVisible = true; // Ensure it's visible
                    block.IsActivePiece = false; // Not active, but should flash
                    block.FlashMode = FlashType.GameOver;
                    block.FlashStartTime = currentTime;
                    block.FlashRate = 2.0f; // 2 Hz = 2 pulses per second
                    board.GameBlocks[x, y] = block;
                    
                    blocksMarked++;
                    // Log.Write(string.Format("TetrisGame: Marked final piece block at ({0}, {1}) for game over flash", x, y));
                }
            }
        }
        
        Log.Write(string.Format("TetrisGame: Game over flash enabled for {0} blocks", blocksMarked));
    }
    
    #endregion
    
    #region Audio System

    // Convert loudness percentage to decibels
    private float LoudnessPercentToDb(float loudnessPercent)
    {
        loudnessPercent = Math.Min(Math.Max(loudnessPercent, 0.0f), 100.0f);
        return 60.0f * (loudnessPercent / 100.0f) - 48.0f;
    }
    
    // Audio playback methods
    private void PlayMoveSound(Vector position)
    {
        // Log.Write(LogLevel.Info, "TetrisGame: PlayMoveSound called");
        if (MoveSound != null)
        {
            PlaySettings playSettings = PlaySettings.PlayOnce;
            playSettings.Loudness = LoudnessPercentToDb(MoveVolume);
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Playing move sound at volume {0}% (loudness: {1}dB)", MoveVolume, playSettings.Loudness));
            ScenePrivate.PlaySoundAtPosition(MoveSound, position, playSettings);
        }
        else
        {
            Log.Write(LogLevel.Warning, "TetrisGame: MoveSound resource not set");
        }
    }
    
    private void PlayRotateSound(Vector position)
    {
        Log.Write(LogLevel.Info, "TetrisGame: PlayRotateSound called");
        if (RotateSound != null)
        {
            PlaySettings playSettings = PlaySettings.PlayOnce;
            playSettings.Loudness = LoudnessPercentToDb(RotateVolume);
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Playing rotate sound at volume {0}% (loudness: {1}dB)", RotateVolume, playSettings.Loudness));
            ScenePrivate.PlaySoundAtPosition(RotateSound, position, playSettings);
        }
        else
        {
            Log.Write(LogLevel.Warning, "TetrisGame: RotateSound resource not set");
        }
    }
    
    private void PlayHardDropSound(Vector position)
    {
        Log.Write(LogLevel.Info, "TetrisGame: PlayHardDropSound called");
        if (HardDropSound != null)
        {
            PlaySettings playSettings = PlaySettings.PlayOnce;
            playSettings.Loudness = LoudnessPercentToDb(HardDropVolume);
            // Log.Write(LogLevel.Info, string.Format("TetrisGame: Playing hard drop sound at volume {0}% (loudness: {1}dB)", HardDropVolume, playSettings.Loudness));
            ScenePrivate.PlaySoundAtPosition(HardDropSound, position, playSettings);
        }
        else
        {
            Log.Write(LogLevel.Warning, "TetrisGame: HardDropSound resource not set");
        }
    }
    
    private void PlayLineClearSound(Vector position)
    {
        Log.Write(LogLevel.Info, "TetrisGame: PlayLineClearSound called");
        if (LineClearSound != null)
        {
            PlaySettings playSettings = PlaySettings.PlayOnce;
            playSettings.Loudness = LoudnessPercentToDb(LineClearVolume);
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Playing line clear sound at volume {0}% (loudness: {1}dB)", LineClearVolume, playSettings.Loudness));
            ScenePrivate.PlaySoundAtPosition(LineClearSound, position, playSettings);
        }
        else
        {
            Log.Write(LogLevel.Warning, "TetrisGame: LineClearSound resource not set");
        }
    }
    
    private void PlayTetrisSound(Vector position)
    {
        Log.Write(LogLevel.Info, "TetrisGame: PlayTetrisSound called");
        if (TetrisSound != null)
        {
            PlaySettings playSettings = PlaySettings.PlayOnce;
            playSettings.Loudness = LoudnessPercentToDb(TetrisVolume);
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Playing Tetris sound at volume {0}% (loudness: {1}dB)", TetrisVolume, playSettings.Loudness));
            ScenePrivate.PlaySoundAtPosition(TetrisSound, position, playSettings);
        }
        else
        {
            Log.Write(LogLevel.Warning, "TetrisGame: TetrisSound resource not set");
        }
    }
    
    private void PlayLockPieceSound(Vector position)
    {
        Log.Write(LogLevel.Info, "TetrisGame: PlayLockPieceSound called");
        if (LockPieceSound != null)
        {
            PlaySettings playSettings = PlaySettings.PlayOnce;
            playSettings.Loudness = LoudnessPercentToDb(LockPieceVolume);
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Playing lock piece sound at volume {0}% (loudness: {1}dB)", LockPieceVolume, playSettings.Loudness));
            ScenePrivate.PlaySoundAtPosition(LockPieceSound, position, playSettings);
        }
        else
        {
            Log.Write(LogLevel.Warning, "TetrisGame: LockPieceSound resource not set");
        }
    }
    
    private void PlayGameOverSound(Vector position)
    {
        Log.Write(LogLevel.Info, "TetrisGame: PlayGameOverSound called");
        if (GameOverSound != null)
        {
            PlaySettings playSettings = PlaySettings.PlayOnce;
            playSettings.Loudness = LoudnessPercentToDb(GameOverVolume);
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Playing game over sound at volume {0}% (loudness: {1}dB)", GameOverVolume, playSettings.Loudness));
            ScenePrivate.PlaySoundAtPosition(GameOverSound, position, playSettings);
        }
        else
        {
            Log.Write(LogLevel.Warning, "TetrisGame: GameOverSound resource not set");
        }
    }
    
    #endregion
    
    #region Chat Interface System
    
    private void ShowWelcomeHint(AgentPrivate agent, SessionId sessionId)
    {
        // Check if agent is still valid
        if (agent != null && agent.IsValid)
        {
            try
            {
                // Set the hint text (truncated to 80 characters as per HintText.cs example)
                string hintText = "Welcome! Type /tet help in nearby chat for commands";
                if (hintText.Length > 80)
                {
                    hintText = hintText.Substring(0, 80);
                }
                
                agent.Client.UI.HintText = hintText;
                Log.Write(LogLevel.Info, "TetrisGame: Displayed welcome hint to " + agent.AgentInfo.Name);
            }
            catch (Exception ex)
            {
                Log.Write(LogLevel.Warning, "TetrisGame: Error showing hint text: " + ex.Message);
            }
            
            // Wait 8 seconds
            Wait(TimeSpan.FromSeconds(8.0f));
            
            // Remove the hint text
            try
            {
                AgentPrivate currentAgent = ScenePrivate.FindAgent(sessionId);
                if (currentAgent != null && currentAgent.IsValid)
                {
                    currentAgent.Client.UI.HintText = null;
                    Log.Write(LogLevel.Info, "TetrisGame: Removed welcome hint from " + currentAgent.AgentInfo.Name);
                }
            }
            catch (Exception ex)
            {
                Log.Write(LogLevel.Warning, "TetrisGame: Error removing hint text: " + ex.Message);
            }
        }
    }
    
    private void OnChatMessage(ChatData data)
    {
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Chat message received: '{0}' from sourceId: {1}", data.Message, data.SourceId));
        
        // Find the sender
        AgentPrivate sender = ScenePrivate.FindAgent(data.SourceId);
        if (sender == null)
        {
            Log.Write(LogLevel.Info, "TetrisGame: Could not find sender agent, ignoring chat");
            return;
        }
        
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Processing chat from: {0}", sender.AgentInfo.Name));
        
        // Parse the message for commands (must start with /tet)
        string message = data.Message.Trim().ToLower();
        string[] chatWords = message.Split(' ');
        
        if (chatWords.Length < 1)
            return;
            
        // Check if it's a /tet command
        if (chatWords[0] != "/tet")
        {
            Log.Write(LogLevel.Info, "TetrisGame: Message doesn't start with /tet, ignoring");
            return;
        }
        
        // Need at least 2 words: "/tet" and the subcommand
        if (chatWords.Length < 2)
        {
            Log.Write(LogLevel.Info, "TetrisGame: /tet command missing subcommand");
            return;
        }
        
        // Build the command for lookup - handle special cases for multi-word commands
        string command;
        if (chatWords.Length >= 3 && chatWords[1] == "bg" && chatWords[2] == "volume")
        {
            command = chatWords[0] + " " + chatWords[1] + " " + chatWords[2];  // "/tet bg volume"
        }
        else if (chatWords.Length >= 3 && chatWords[1] == "help" && chatWords[2] == "mode")
        {
            command = chatWords[0] + " " + chatWords[1] + " " + chatWords[2];  // "/tet help mode"
        }
        else
        {
            command = chatWords[0] + " " + chatWords[1];  // "/tet help", "/tet bg", etc.
        }
        
        if (!commandsUsage.ContainsKey(command))
        {
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Unknown /tet command: {0}", command));
            return;
        }
            
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Processing command '{0}' from {1}", command, sender.AgentInfo.Name));
        
        // Route to appropriate GLOBAL command handler
        // Note: Board-specific commands (/tet reset, /tet restart, /tet start, /tet score, /tet status) 
        // are now handled by individual TetrisGameBoard chat subscriptions
        try
        {
            // Handle global commands only (anyone can use these)
            switch (command)
            {
                case "/tet help":
                    SendHelpMessage(sender);
                    break;
                case "/tet add":
                    HandleAddBoardCommand(sender);
                    break;
                case "/tet leaderboard":
                case "/tet leader":
                    SendLeaderboard(sender);
                    break;
                case "/tet bg":
                    ToggleBackgroundMusic(sender);
                    break;
                case "/tet bg volume":
                    if (chatWords.Length >= 4)
                    {
                        SetBackgroundMusicVolume(sender, chatWords[3]);
                    }
                    else
                    {
                        sender.SendChat("Usage: /tet bg volume [0-100]");
                    }
                    break;
                case "/tet score":
                    ShowAllBoardScores(sender);
                    break;
                case "/tet mode":
                    if (chatWords.Length >= 3)
                    {
                        HandleModeCommand(sender, chatWords);
                    }
                    else
                    {
                        sender.SendChat("Usage: /tet mode <1|2> [seconds for mode 2]");
                    }
                    break;
                case "/tet help mode":
                    SendGameModeHelp(sender);
                    break;
                default:
                    // Board-specific commands are handled by individual boards
                    // Ignore unknown commands silently (they might be handled by boards)
                    Log.Write(LogLevel.Info, string.Format("TetrisGame: Command '{0}' not a global command, ignoring (may be handled by board)", command));
                    break;
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: Error processing chat command: " + ex.Message);
        }
    }
    
    private void SendHelpMessage(AgentPrivate agent)
    {
        try
        {
            string helpMessage = "Tetris Chat Commands:";
            helpMessage += "\n/tet help - (Anyone) Show this command list";
            helpMessage += "\n/tet leaderboard - (Anyone) Show top 10 scores";
            helpMessage += "\n/tet leader - (Anyone) Show top 10 scores";
            helpMessage += "\n/tet bg - (Anyone) Toggle bg music on/off";
            helpMessage += "\n/tet bg volume [0-100] - (Anyone) Set music volume";
            helpMessage += "\n/tet score - (Anyone) Show all active games";
            helpMessage += "\n/tet mode - (Anyone) Set game mode";
            helpMessage += "\n/tet help mode - (Anyone) List game modes";
            helpMessage += "\n/tet reset - (Seated) Start a new game";
            helpMessage += "\n/tet restart - (Seated) Start a new game";
            helpMessage += "\n/tet start - (Seated) Start a new game";
            helpMessage += "\n/tet ghost - (Seated) Toggle ghost piece on/off";
            
            agent.SendChat(helpMessage);
            Log.Write(LogLevel.Info, "TetrisGame: Sent help message to " + agent.AgentInfo.Name);
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: Error sending help message: " + ex.Message);
        }
    }
    
    
    
    #endregion
    
    #region Leaderboard System
    
    private void LoadLeaderboard(DataStore.Result<List<LeaderboardEntry>> result)
    {
        Log.Write(LogLevel.Info, "TetrisGame: LoadLeaderboard() callback called");
        Log.Write(LogLevel.Info, string.Format("TetrisGame: DataStore result - Success: {0}", result.Success));
        
        if (result.Success)
        {
            leaderboard = result.Object;
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Successfully loaded {0} leaderboard entries", leaderboard.Count));
            
            // Log each entry for debugging
            for (int i = 0; i < leaderboard.Count; i++)
            {
                var entry = leaderboard[i];
                Log.Write(LogLevel.Info, string.Format("TetrisGame: Leaderboard[{0}]: {1} - Score: {2}, Level: {3}, Lines: {4}, Date: {5}", 
                    i, entry.Handle, entry.Score, entry.Level, entry.Lines, entry.Date));
            }
        }
        else
        {
            // If no leaderboard exists yet, create an empty one
            leaderboard = new List<LeaderboardEntry>();
            Log.Write(LogLevel.Info, "TetrisGame: No existing leaderboard found, starting fresh");
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Load failure reason: {0}", result.Message));
            Log.Write(LogLevel.Info, string.Format("TetrisGame: JsonString: {0}", result.JsonString ?? "null"));
        }
        
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Final leaderboard state - Count: {0}", leaderboard.Count));
    }
    
    private void SaveLeaderboard()
    {
        Log.Write(LogLevel.Info, "TetrisGame: SaveLeaderboard() called");
        
        if (leaderboardStore != null)
        {
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Attempting to save {0} leaderboard entries with key '{1}'", leaderboard.Count, leaderboardKey));
            
            // Log what we're trying to save
            for (int i = 0; i < leaderboard.Count; i++)
            {
                var entry = leaderboard[i];
                Log.Write(LogLevel.Info, string.Format("TetrisGame: Saving entry[{0}]: {1} - Score: {2}, Level: {3}, Lines: {4}", 
                    i, entry.Handle, entry.Score, entry.Level, entry.Lines));
            }
            
            leaderboardStore.Store(leaderboardKey, leaderboard, (result) =>
            {
                Log.Write(LogLevel.Info, string.Format("TetrisGame: SaveLeaderboard callback - Success: {0}", result.Success));
                
                if (result.Success)
                {
                    Log.Write(LogLevel.Info, "TetrisGame: Leaderboard saved successfully to DataStore!");
                    Log.Write(LogLevel.Info, string.Format("TetrisGame: Save result version: {0}", result.Version));
                }
                else
                {
                    Log.Write(LogLevel.Warning, string.Format("TetrisGame: FAILED to save leaderboard: {0}", result.Message));
                    Log.Write(LogLevel.Warning, string.Format("TetrisGame: Save failure JsonString: {0}", result.JsonString ?? "null"));
                }
            });
        }
        else
        {
            Log.Write(LogLevel.Error, "TetrisGame: Cannot save leaderboard - leaderboardStore is null!");
        }
    }
    
    private bool IsHighScore(int score)
    {
        if (score <= 0) return false;
        if (leaderboard.Count < 10) return true;
        return score > leaderboard[leaderboard.Count - 1].Score;
    }
    
    private void UpdateLeaderboard(AgentPrivate player, int score, int level, int lines)
    {
        Log.Write(LogLevel.Info, "TetrisGame: UpdateLeaderboard() called");
        
        if (player == null || !player.IsValid || score <= 0) 
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: UpdateLeaderboard() early return - player valid: {0}, score: {1}", 
                (player != null && player.IsValid), score));
            return;
        }
        
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Current leaderboard count before update: {0}", leaderboard.Count));
        
        LeaderboardEntry newEntry = new LeaderboardEntry
        {
            Handle = player.AgentInfo.Handle,
            Score = score,
            Level = level,
            Lines = lines,
            Date = DateTime.UtcNow
        };
        
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Created new entry - Handle: {0}, Score: {1}, Level: {2}, Lines: {3}", 
            newEntry.Handle, newEntry.Score, newEntry.Level, newEntry.Lines));
        
        // Add the new entry
        leaderboard.Add(newEntry);
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Added entry to leaderboard. New count: {0}", leaderboard.Count));
        
        // Sort by score (descending)
        leaderboard.Sort((a, b) => b.Score.CompareTo(a.Score));
        Log.Write(LogLevel.Info, "TetrisGame: Sorted leaderboard by score (descending)");
        
        // Keep only top 10
        if (leaderboard.Count > 10)
        {
            int removedCount = leaderboard.Count - 10;
            leaderboard.RemoveRange(10, removedCount);
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Trimmed leaderboard - removed {0} entries, new count: {1}", removedCount, leaderboard.Count));
        }
        
        // Log final leaderboard state
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Final leaderboard state after update ({0} entries):", leaderboard.Count));
        for (int i = 0; i < leaderboard.Count; i++)
        {
            var entry = leaderboard[i];
            Log.Write(LogLevel.Info, string.Format("TetrisGame: #{0}: {1} - {2} points", i+1, entry.Handle, entry.Score));
        }
        
        // Save to persistent storage
        SaveLeaderboard();
        
        Log.Write(LogLevel.Info, string.Format("TetrisGame: Successfully added {0} to leaderboard with score {1}", player.AgentInfo.Handle, score));
    }
    
    private string FormatLeaderboard()
    {
        if (leaderboard.Count == 0)
        {
            return "=== TOP 10 LEADERBOARD ===\nNo scores yet! Be the first!";
        }
        
        string leaderboardText = "=== TOP 10 LEADERBOARD ===\n";
        for (int i = 0; i < leaderboard.Count; i++)
        {
            LeaderboardEntry entry = leaderboard[i];
            string dateStr = entry.Date.ToString("MM/dd HH:mm");
            leaderboardText += string.Format("{0}. {1} - {2} pts (L{3}, {4} lines) - {5}\n",
                i + 1, entry.Handle, entry.Score, entry.Level, entry.Lines, dateStr);
        }
        return leaderboardText;
    }
    
    private void SendLeaderboard(AgentPrivate agent)
    {
        Log.Write(LogLevel.Info, string.Format("TetrisGame: SendLeaderboard() called by {0}", agent.AgentInfo.Name));
        
        try
        {
            if (leaderboardStore == null)
            {
                Log.Write(LogLevel.Warning, "TetrisGame: Leaderboard requested but leaderboardStore is null");
                agent.SendChat("Leaderboard is not available (disabled or failed to initialize).");
                return;
            }
            
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Formatting leaderboard with {0} entries", leaderboard.Count));
            
            // Debug: Log current leaderboard state when requested
            Log.Write(LogLevel.Info, "TetrisGame: Current leaderboard state when requested:");
            for (int i = 0; i < leaderboard.Count; i++)
            {
                var entry = leaderboard[i];
                Log.Write(LogLevel.Info, string.Format("TetrisGame: Entry[{0}]: {1} - Score: {2}, Level: {3}, Lines: {4}, Date: {5}", 
                    i, entry.Handle, entry.Score, entry.Level, entry.Lines, entry.Date));
            }
            
            string leaderboardMessage = FormatLeaderboard();
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Formatted leaderboard message: {0}", leaderboardMessage));
            
            agent.SendChat(leaderboardMessage);
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Successfully sent leaderboard to {0}", agent.AgentInfo.Name));
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Error sending leaderboard to {0}: {1}", agent.AgentInfo.Name, ex.Message));
        }
    }
    
    private void ShowAllBoardScores(AgentPrivate agent)
    {
        try
        {
            Log.Write(LogLevel.Info, string.Format("TetrisGame: ShowAllBoardScores() called by {0}", agent.AgentInfo.Name));
            
            List<string> activeGames = new List<string>();
            
            // Check each board for active games
            foreach (var board in gameBoards)
            {
                if (board != null && board.AssignedPlayer != null && board.CurrentState == GameState.Playing)
                {
                    string gameInfo = string.Format("Board {0}: {1} - Level {2}, Score {3}", 
                        board.BoardIndex, 
                        board.AssignedPlayer.AgentInfo.Name,
                        board.Level,
                        board.Score);
                    activeGames.Add(gameInfo);
                }
            }
            
            // Build mode info
            string modeInfo = "Current Mode: ";
            switch (currentGameMode)
            {
                case GameMode.Normal:
                    modeInfo += "Normal";
                    break;
                case GameMode.Speed:
                    modeInfo += "Speed (4x faster progression)";
                    break;
                case GameMode.Timed:
                    if (timedModeActive)
                    {
                        double elapsedSeconds = (double)(Stopwatch.GetTimestamp() - timedModeStartTime) / Stopwatch.Frequency;
                        double remainingSeconds = Math.Max(0, timedModeSeconds - elapsedSeconds);
                        modeInfo += string.Format("Timed ({0:F0}s remaining)", remainingSeconds);
                    }
                    else
                    {
                        modeInfo += string.Format("Timed ({0}s - not started)", timedModeSeconds);
                    }
                    break;
            }
            
            // Send the results
            if (activeGames.Count > 0)
            {
                string message = modeInfo + "\nActive Tetris Games:\n" + string.Join("\n", activeGames.ToArray());
                agent.SendChat(message);
            }
            else
            {
                agent.SendChat(modeInfo + "\nNo active Tetris games at the moment.");
            }
            
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Sent {0} active game scores to {1}", activeGames.Count, agent.AgentInfo.Name));
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Error showing all board scores: {0}", ex.Message));
            agent.SendChat("Error retrieving game scores.");
        }
    }
    
    private void SendChatToAll(string message)
    {
        try
        {
            foreach (var player in allPlayers)
            {
                if (player != null && player.IsValid)
                {
                    player.SendChat(message);
                }
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Error sending chat to all: {0}", ex.Message));
        }
    }
    
    private void EndTimedMode()
    {
        timedModeActive = false;
        
        Log.Write("TetrisGame: Timed mode ended");
        SendChatToAll("Time's up! Round ended.");
        
        // Collect scores from all active games
        List<BoardScore> scores = new List<BoardScore>();
        foreach (var board in gameBoards)
        {
            if (board != null && board.AssignedPlayer != null && board.CurrentState == GameState.Playing)
            {
                scores.Add(new BoardScore 
                { 
                    PlayerName = board.AssignedPlayer.AgentInfo.Name,
                    Score = board.Score,
                    Level = board.Level,
                    BoardIndex = board.BoardIndex
                });
                
                // End the game
                board.EndGame(this);
            }
        }
        
        // Find and announce winner
        if (scores.Count > 0)
        {
            // Sort by score descending
            scores.Sort((a, b) => b.Score.CompareTo(a.Score));
            
            string results = "=== TIMED ROUND RESULTS ===";
            for (int i = 0; i < scores.Count; i++)
            {
                var score = scores[i];
                results += string.Format("\n{0}. {1} - Level {2}, Score {3}", 
                    i + 1, score.PlayerName, score.Level, score.Score);
            }
            
            SendChatToAll(results);
            
            if (scores.Count > 0)
            {
                SendChatToAll(string.Format("🏆 Winner: {0} with {1} points!", 
                    scores[0].PlayerName, scores[0].Score));
            }
        }
        else
        {
            SendChatToAll("No active games to score.");
        }
        
        // Keep the current game mode for next round
        SendChatToAll(string.Format("Ready for next round! Mode: {0}{1}", 
            currentGameMode.ToString(),
            currentGameMode == GameMode.Timed ? string.Format(" ({0} seconds)", timedModeSeconds) : ""));
    }
    
    private class BoardScore
    {
        public string PlayerName;
        public int Score;
        public int Level;
        public int BoardIndex;
    }
    
    private void HandleModeCommand(AgentPrivate agent, string[] chatWords)
    {
        try
        {
            // Check if any games are active
            bool hasActiveGames = false;
            foreach (var board in gameBoards)
            {
                if (board != null && board.CurrentState == GameState.Playing)
                {
                    hasActiveGames = true;
                    break;
                }
            }
            
            if (hasActiveGames)
            {
                agent.SendChat("Cannot change game mode while games are in progress.");
                return;
            }
            
            // Parse mode number
            int modeNum;
            if (!int.TryParse(chatWords[2], out modeNum))
            {
                agent.SendChat("Invalid mode number. Use: /tet mode 1 (speed) or /tet mode 2 <seconds> (timed)");
                return;
            }
            
            switch (modeNum)
            {
                case 0:
                    currentGameMode = GameMode.Normal;
                    agent.SendChat("Game mode set to: Normal");
                    Log.Write("TetrisGame: Game mode changed to Normal");
                    break;
                    
                case 1:
                    currentGameMode = GameMode.Speed;
                    agent.SendChat("Game mode set to: Speed (4x faster progression)");
                    Log.Write("TetrisGame: Game mode changed to Speed");
                    break;
                    
                case 2:
                    if (chatWords.Length < 4)
                    {
                        agent.SendChat("Timed mode requires duration. Usage: /tet mode 2 <seconds>");
                        return;
                    }
                    
                    float seconds;
                    if (!float.TryParse(chatWords[3], out seconds) || seconds <= 0 || seconds > 600)
                    {
                        agent.SendChat("Invalid duration. Please specify 1-600 seconds.");
                        return;
                    }
                    
                    currentGameMode = GameMode.Timed;
                    timedModeSeconds = seconds;
                    timedModeActive = false; // Will activate when first game starts
                    agent.SendChat(string.Format("Game mode set to: Timed ({0} seconds)", seconds));
                    Log.Write(string.Format("TetrisGame: Game mode changed to Timed ({0} seconds)", seconds));
                    break;
                    
                default:
                    agent.SendChat("Unknown mode. Use: 0=normal, 1=speed, 2=timed");
                    break;
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Error handling mode command: {0}", ex.Message));
            agent.SendChat("Error setting game mode.");
        }
    }
    
    private void SendGameModeHelp(AgentPrivate agent)
    {
        try
        {
            string helpMessage = "=== Tetris Game Modes ===";
            helpMessage += "\n";
            helpMessage += "\n/tet mode 0 - Normal Mode";
            helpMessage += "\n  • Standard Tetris gameplay";
            helpMessage += "\n  • Speed increases 20% per level";
            helpMessage += "\n";
            helpMessage += "\n/tet mode 1 - Speed Mode";
            helpMessage += "\n  • 4x faster speed progression";
            helpMessage += "\n  • Speed increases 80% per level";
            helpMessage += "\n  • For experienced players";
            helpMessage += "\n";
            helpMessage += "\n/tet mode 2 <seconds> - Timed Mode";
            helpMessage += "\n  • Competitive round with time limit";
            helpMessage += "\n  • All players compete simultaneously";
            helpMessage += "\n  • Winner announced when time expires";
            helpMessage += "\n  • Example: /tet mode 2 120 (2 minutes)";
            helpMessage += "\n";
            helpMessage += string.Format("\nCurrent mode: {0}", GetCurrentModeDescription());
            
            agent.SendChat(helpMessage);
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Sent game mode help to {0}", agent.AgentInfo.Name));
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Warning, string.Format("TetrisGame: Error sending game mode help: {0}", ex.Message));
            agent.SendChat("Error displaying game mode help.");
        }
    }
    
    private string GetCurrentModeDescription()
    {
        switch (currentGameMode)
        {
            case GameMode.Normal:
                return "Normal";
            case GameMode.Speed:
                return "Speed (4x faster)";
            case GameMode.Timed:
                if (timedModeActive)
                {
                    double elapsedSeconds = (double)(Stopwatch.GetTimestamp() - timedModeStartTime) / Stopwatch.Frequency;
                    double remainingSeconds = Math.Max(0, timedModeSeconds - elapsedSeconds);
                    return string.Format("Timed ({0:F0}s remaining)", remainingSeconds);
                }
                else
                {
                    return string.Format("Timed ({0}s - not started)", timedModeSeconds);
                }
            default:
                return "Unknown";
        }
    }
    
    #endregion
    
    #region Background Music System
    
    private void StartBackgroundMusic()
    {
        if (!musicSystemInitialized || !musicEnabled)
        {
            Log.Write(LogLevel.Info, "TetrisGame: Background music not started - system not initialized or disabled");
            return;
        }
        
        Log.Write(LogLevel.Info, "TetrisGame: Starting background music loop");
        StartCoroutine(BackgroundMusicLoop);
    }
    
    private void StopBackgroundMusic()
    {
        if (currentMusicHandle != null && currentMusicHandle.IsPlaying())
        {
            currentMusicHandle.Stop();
            Log.Write(LogLevel.Info, "TetrisGame: Stopped background music");
        }
        currentMusicHandle = null;
    }
    
    private void BackgroundMusicLoop()
    {
        while (musicEnabled && musicSystemInitialized && validMusicTracks != null && validMusicTracks.Length > 0)
        {
            // Get current track from valid tracks array
            SoundResource currentTrack = validMusicTracks[currentTrackIndex];
            
            PlayCurrentTrack(currentTrack);
            
            // Wait for track to finish playing
            while (currentMusicHandle != null && currentMusicHandle.IsPlaying() && musicEnabled)
            {
                Wait(TimeSpan.FromSeconds(1.0f)); // Check every second
            }
            
            // 5-second delay between tracks
            if (musicEnabled)
            {
                Log.Write(LogLevel.Info, "TetrisGame: 5-second delay between tracks");
                Wait(TimeSpan.FromSeconds(5.0f));
            }
            
            // Move to next track (cycle through only valid tracks)
            currentTrackIndex = (currentTrackIndex + 1) % validMusicTracks.Length;
            
            // If music was disabled during loop, exit
            if (!musicEnabled)
            {
                Log.Write(LogLevel.Info, "TetrisGame: Background music loop exiting - music disabled");
                break;
            }
        }
    }
    
    private void PlayCurrentTrack(SoundResource track)
    {
        try
        {
            PlaySettings playSettings = PlaySettings.PlayOnce;
            playSettings.Loudness = LoudnessPercentToDb(currentMusicVolume);
            
            currentMusicHandle = ScenePrivate.PlaySound(track, playSettings);
            Log.Write(LogLevel.Info, string.Format("TetrisGame: Playing background music track {0} at volume {1}%", currentTrackIndex + 1, currentMusicVolume));
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: Error playing background music: " + ex.Message);
        }
    }
    
    private void ToggleBackgroundMusic(AgentPrivate agent)
    {
        try
        {
            musicEnabled = !musicEnabled;
            
            if (musicEnabled)
            {
                agent.SendChat("Background music enabled.");
                Log.Write(LogLevel.Info, "TetrisGame: Background music enabled via chat command");
                StartCoroutine(StartBackgroundMusic);
            }
            else
            {
                agent.SendChat("Background music disabled.");
                Log.Write(LogLevel.Info, "TetrisGame: Background music disabled via chat command");
                StopBackgroundMusic();
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: Error toggling background music: " + ex.Message);
        }
    }
    
    private void SetBackgroundMusicVolume(AgentPrivate agent, string volumeStr)
    {
        try
        {
            float volume;
            if (float.TryParse(volumeStr, out volume))
            {
                if (volume >= 0.0f && volume <= 100.0f)
                {
                    currentMusicVolume = volume;
                    agent.SendChat(string.Format("Background music volume set to {0}%", (int)volume));
                    Log.Write(LogLevel.Info, string.Format("TetrisGame: Background music volume set to {0}% via chat command", volume));
                    
                    // Update current playing track volume if music is playing
                    if (currentMusicHandle != null && currentMusicHandle.IsPlaying())
                    {
                        // Note: Sansar doesn't support changing volume of already playing sounds
                        // The new volume will apply to the next track
                        agent.SendChat("Volume will apply to next track.");
                    }
                }
                else
                {
                    agent.SendChat("Volume must be between 0 and 100.");
                }
            }
            else
            {
                agent.SendChat("Invalid volume value. Use a number between 0 and 100.");
            }
        }
        catch (Exception ex)
        {
            Log.Write(LogLevel.Warning, "TetrisGame: Error setting background music volume: " + ex.Message);
        }
    }
    
    #endregion
}