# Sansar Tetris - Multi-Board Edition

A multiplayer Tetris game for Sansar supporting multiple simultaneous players, each with their own game board. Features dynamic board spawning, global game modes, and a unified single-script architecture.

## Features

- **Multi-Board Support**: Dynamic spawning of multiple game boards for simultaneous players
- **Unified Architecture**: Single script managing all boards - no inter-script communication issues
- **Game Modes**: Normal, Speed (4x faster), and Timed (competitive rounds with winner announcement)
- **Per-Board Grid System**: Each board has 400+ blocks
  - 200 game blocks: Dynamic visibility and color control for active gameplay
  - 200 background blocks: Multi-material grid with subtle edge highlighting
  - 32 preview blocks: Next piece display area
- **Advanced Material Control**: Multi-material support for background blocks with Base + Highlight1
- **Queue-Based Spawning**: Sequential block creation prevents throttle exceptions
- **Chat Commands**: Global commands for all players, board-specific commands for seated players
- **Welcome Hints**: Automatic UI hints for new players joining the world
- **SRS Rotation**: Super Rotation System with adjusted I-piece kicks
- **Persistent Settings**: Game modes and settings persist between rounds
- **Leaderboard System**: Top 10 all-time high scores tracked persistently

## Controls

### Getting Started
1. **Find an available Tetris seat** - Look for chairs without players
2. **Click the seat** to sit down and claim that board
3. **Game starts automatically** or use `/tet start` to begin a new game
4. **Stand up** from the seat to exit and free the board for others

### Game Controls (while seated)
- **A**: Move piece left (Keypad4)
- **D**: Move piece right (Keypad6)
- **S**: Soft drop (move down faster) (Keypad2)
- **W**: Slow down piece (extends time before auto-drop) (Keypad8)
- **R**: Rotate piece clockwise (SecondaryAction)
- **F**: Hard drop (instant fall to bottom) (PrimaryAction)
- **1**: Rotate piece counter-clockwise (Action1)

### Chat Commands

#### Global Commands (Anyone can use)
- `/tet help` - Show all available commands
- `/tet help mode` - Show game mode information
- `/tet add` - Add a new game board
- `/tet score` - Show all active game scores
- `/tet mode <number> [duration]` - Set game mode:
  - `0` - Normal mode
  - `1` - Speed mode (4x faster progression)
  - `2 <seconds>` - Timed mode (e.g., `/tet mode 2 120` for 2-minute rounds)
- `/tet leaderboard` or `/tet leader` - Show top 10 all-time scores

#### Board-Specific Commands (For seated players)
- `/tet start` - Start a new game
- `/tet pause` - Pause/unpause your game
- `/tet music` - Toggle background music
- `/tet volume <0-100>` - Set music volume
- `/tet track <1-6>` - Select music track

## Script Files

### Current Architecture (Unified Single Script)
- **TetrisGame.cs**: All-in-one script managing multiple game boards

### Architecture Overview

**Multi-Board Design**: Single script manages multiple independent game boards with queue-based spawning.

```
TetrisGame.cs ────┬─── Multi-Board Management (List<TetrisGameBoard>)
(Single Script)   │    Dynamic Chair/Grid Spawning (ScenePrivate.CreateCluster)
                  │    Queue-Based Block Creation (prevents throttling)
                  │    Player Assignment (per-board seat detection)
                  │    Global Game Modes (affects all boards)
                  │    Chat Command System (global + board-specific)
                  │    
                  ▼
Per Board ────────┬─── 200 Game Blocks (dynamic visibility/color)
(400+ blocks)     │    200 Background Blocks (multi-material grid)
                  │    32 Preview Blocks (next piece display)
                  │    Independent game state and scoring
                  │    Direct material control via MeshComponent
```

### Key Architectural Improvements

1. **Multi-Board Support**: Dynamic spawning and management of multiple independent game boards
2. **Queue-Based Spawning**: Prevents `ScenePrivate.CreateCluster()` throttle exceptions
3. **Unified Script**: All functionality in one script - no inter-script communication
4. **Board Independence**: Each `TetrisGameBoard` instance maintains complete game state
5. **Global Game Modes**: Speed and Timed modes affect all boards simultaneously
6. **Persistent Settings**: Game modes and configurations persist between rounds
7. **Multi-Material Backgrounds**: Separate BackgroundBlock resource with edge highlighting

### Player Input Discovery

Through testing, we discovered that Sansar only passes specific command events to scripts:
- **Keypad4** = A key (Move left)
- **Keypad6** = D key (Move right)
- **Keypad2** = S key (Soft drop)
- **Keypad8** = W key (Slow down)
- **SecondaryAction** = R key (Rotate)
- **PrimaryAction** = F key (Hard drop)
- **Action1** = 1 key (Rotate counter-clockwise)

The proper pattern is:
```csharp
// Get the agent when they sit down
currentPlayer = ScenePrivate.FindAgent(data.ObjectId);

// Subscribe to their commands
agent.Client.SubscribeToCommand("Keypad4", CommandAction.Pressed, HandleMoveLeft, null);
```

## In-Game Setup Instructions

### Step 1: Create the Game Block (GenericBlock)
1. In Sansar, create a new object (simple cube works best)
2. Make the object small (0.9 x 0.9 x 0.9 units recommended)
3. Apply a material that supports scripting (Standard Plus Emissive shader recommended)
4. **Critical**: Ensure material has both **Tint** and **Emissive** properties enabled
5. **Important**: Mark the mesh as **Scriptable** in the editor
6. Save to your inventory as a ClusterResource named "TetrisBlock"

### Step 2: Create the Background Block (BackgroundBlock)
1. Create another cube object for the background grid
2. Apply a multi-material setup:
   - **Base Material**: Named "Base" - for main surfaces (will be black)
   - **Highlight1 Material**: Named "Highlight1" - for edges (dark gray with slight emissive)
3. Configure UV mapping so edges use Highlight1 material
4. Mark the mesh as **Scriptable**
5. Save as ClusterResource named "BackgroundBlock"

### Step 3: Create the Chair Resource
1. Use any chair/seat model
2. Add **sit points** in the Sansar editor
3. Save as ClusterResource named "TetrisChair"

### Step 4: Scene Setup
Place a single object in your scene and attach `TetrisGame.cs`:

#### Script Properties
- `GenericBlock` = TetrisBlock resource (game pieces)
- `BackgroundBlock` = BackgroundBlock resource (grid reference)
- `ChairResource` = TetrisChair resource (player seats)
- `ChairOrigin` = Position for first chair (e.g., 0,0,0)
- `GridOrigin` = Bottom-left corner of first board (e.g., 0,0,2)
- `DefaultBoardCount` = Number of boards to spawn initially (e.g., 3)
- `ChairSpacing` = Distance between chairs (default: 2.0)
- `BatchSize` = Blocks per spawn batch (default: 10)
- `EnableMultiMaterial` = true (for background block materials)

#### Optional Music Setup
- `BackgroundMusic0-5` = SoundResource files for background music
- `LineClearSound` = Sound effect for clearing lines
- `RotateSound` = Sound effect for piece rotation

### Step 5: Launch
1. Start your scene
2. Multiple chairs will spawn based on `DefaultBoardCount`
3. Background music starts automatically
4. Players can sit in any available chair to play
5. Use `/tet add` to spawn additional boards dynamically

## Game Flow

1. **Scene Initialization**: 
   - TetrisGame spawns chairs based on `DefaultBoardCount`
   - Each board spawns blocks through queue system (prevents throttling)
   - Background blocks spawn first, then game blocks, then preview area
   
2. **Player Interaction**: 
   - Players find available chairs and sit down
   - Board automatically assigns to seated player
   - Welcome hint displays: "Welcome! Type /tet help in nearby chat for commands"
   
3. **Game Start**: 
   - Game starts automatically or via `/tet start`
   - If in Timed mode, timer starts with first player
   
4. **Gameplay**: 
   - Standard Tetris mechanics with SRS rotation
   - Key repeat system for smooth movement
   - Line clearing with flash effects
   - Score multipliers based on game mode
   
5. **Game End**: 
   - Normal/Speed: Player stands up or tops out
   - Timed: All games end when timer expires, winner announced
   - Board becomes available for next player

## Technical Details

### Multi-Board Management
Each `TetrisGameBoard` instance maintains complete state:
```csharp
public class TetrisGameBoard
{
    // Board identification
    public int BoardIndex;
    public AgentPrivate AssignedPlayer;
    
    // Game state
    public GameState CurrentState;
    public int Score, Level, TotalLinesCleared;
    
    // Block arrays
    public TetrisGameBlock[,] GameBlocks;
    public TetrisBackgroundBlock[,] BackgroundBlocks;
    public TetrisPreviewBlock[,] PreviewBlocks;
    
    // Spawned objects
    public ObjectPrivate ChairObject;
    public RigidBodyComponent ChairRigidBody;
}
```

### Queue-Based Spawning System
Prevents throttle exceptions when creating hundreds of blocks:
```csharp
// Queue system for sequential board processing
private Queue<TetrisGameBoard> boardSpawnQueue = new Queue<TetrisGameBoard>();

// Add board to queue when chair spawns
private void OnChairSpawned(Cluster cluster, TetrisGameBoard board)
{
    boardSpawnQueue.Enqueue(board);
}

// Process one board at a time
private void ProcessBoardSpawnQueue()
{
    while (boardSpawnQueue.Count > 0)
    {
        TetrisGameBoard board = boardSpawnQueue.Dequeue();
        SpawnBlocksForBoard(board); // Creates 400+ blocks
        Wait(TimeSpan.FromSeconds(0.5)); // Delay between boards
    }
}
```

### Material Control System

#### Game Blocks
Single material with dynamic properties:
- **Tint**: Color based on piece type (Cyan=I, Yellow=O, Purple=T, etc.)
- **Emissivity**: Glow effects for active pieces (2.0f) and line clearing (15.0f)
- **Visibility**: Hidden when not in use via `MeshComponent.SetIsVisible()`

#### Background Blocks
Multi-material system with Base + Highlight1:
```csharp
// Base material - black surfaces
var baseMaterial = mesh.GetRenderMaterial("Base");
baseProps.Tint = BackgroundBaseColor; // Black
baseProps.EmissiveIntensity = 0.0f;

// Highlight1 material - gray edges
var highlight1Material = mesh.GetRenderMaterial("Highlight1");
highlight1Props.Tint = BackgroundHighlightColor; // Dark gray (0.2, 0.2, 0.2)
highlight1Props.EmissiveIntensity = BackgroundHighlightEmissive; // 0.1
```

### Game Modes

#### Speed Mode
- Drop interval multiplier: 0.2 (vs 0.8 normal)
- Results in 4x faster level progression
- Activated with `/tet mode 1`

#### Timed Mode
- Configurable round duration (e.g., 120 seconds)
- All active games end simultaneously
- Winner announced with final scores
- Mode persists for subsequent rounds
- Activated with `/tet mode 2 <seconds>`

## Troubleshooting

### Common Issues

- **Blocks not spawning**: Check that all ClusterResources are properly assigned
- **Throttle exceptions**: Reduce `BatchSize` or increase `BatchDelay`
- **Background not showing grid lines**: 
  - Ensure BackgroundBlock has Base and Highlight1 materials
  - Check that EnableMultiMaterial is set to true
  - Verify UV mapping puts Highlight1 on edges
- **Chair not working**: Confirm sit points are configured in Sansar editor
- **No welcome hint**: Check player tracking system initialization
- **Timed mode not ending**: Verify timer is starting with first player

### Performance Tuning

- **DefaultBoardCount**: Start with 3-4 boards, add more with `/tet add`
- **BatchSize**: Default 10 works well, reduce to 5 if throttling occurs
- **ChairSpacing**: Increase if boards feel too close together
- **BOARD_SPACING_X**: Currently 17 units between grids

### Log Messages to Monitor
- `"TetrisGame: Board X added to spawn queue"`
- `"TetrisGame: Starting block spawn for Board X"`
- `"TetrisGame: Material[0] - HasTint: True, HasEmissive: True"` (for debugging materials)
- `"TetrisGame: Timed mode started - X seconds"`
- `"TetrisGame: Player joined: [name] (Total players: X)"`

## Removed Files (V1 Architecture Cleanup)

The following files were removed as part of the streamlined architecture:
- **BlockController.cs** - Replaced by direct material control in GridManager
- **PieceController.cs** - Functionality moved to GameManager

If you have older versions of these files in your Sansar project, they are no longer needed and can be safely removed.