# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a **Sansar Tetris implementation** using a unified single-script architecture with multi-board support. The game dynamically spawns multiple game boards, each with 400 blocks (200 game blocks + 200 background blocks) in a 10x20 grid. It controls visibility, color, and effects rather than moving physical objects. All functionality is contained in a single `TetrisGame.cs` script that manages multiple independent game boards, eliminating inter-script communication complexity.

## Build System

### Local Compilation Setup
Scripts are compiled locally using the .NET Framework C# compiler to catch errors before uploading to Sansar:

```bash
# Build the unified Tetris game script
"/mnt/c/Windows/Microsoft.NET/Framework64/v4.0.30319/csc.exe" /target:library /reference:"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Script.dll" /reference:"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Simulation.dll" /out:"bin/TetrisGame.dll" TetrisGame.cs
```

## Architecture Overview

### Unified Single-Script Multi-Board Architecture
The game uses a **single TetrisGame.cs script** that manages multiple independent game boards:

1. **Dynamic Board Spawning** - Creates chairs and game grids based on `DefaultBoardCount` parameter
2. **Board Independence** - Each `TetrisGameBoard` instance maintains its own game state
3. **Player Assignment** - Players claim boards by sitting in spawned chairs
4. **Queue-Based Spawning** - Sequential block spawning to avoid throttle exceptions
5. **Material Control** - Direct mesh manipulation via `MeshComponent.GetRenderMaterials()`
6. **Global Game Modes** - Speed, Normal, and Timed modes affect all boards simultaneously
7. **Chat Commands** - Global commands (/tet add, /tet mode) and board-specific controls

### Communication Patterns

**Direct Method Calls Only:**
- No event-based messaging between scripts
- All methods called directly within the same class
- Immediate visual updates without event delays
- Direct array access to block objects and mesh components

### Grid-Based Architecture
Unlike traditional Tetris, this implementation:
- **Never moves objects** - only changes visibility and materials
- **Spawns blocks per board** using `ScenePrivate.CreateCluster()`
  - 200 game blocks per board for active gameplay
  - 200 background blocks per board for visual grid reference
  - Preview area with 16 blocks + 16 background blocks per board
- **Represents pieces as data patterns** that control which blocks are visible
- **Clears lines by hiding blocks** and shifting grid state arrays
- **Uses visibility control** via `MeshComponent.SetIsVisible()` for performance
- **Key repeat system** for smooth left/right movement without event spam

### Multi-Board System
- **Board Spawning**: Chairs spawn at `ChairOrigin` with `ChairSpacing` between them
- **Grid Positioning**: Each board's grid is offset by `BOARD_SPACING_X` (17 units)
- **Resource Management**: All blocks are spawned through a queue system to prevent throttling
- **Board State**: Each `TetrisGameBoard` tracks its own:
  - Game state (WaitingForPlayer, Playing, GameOver, etc.)
  - Current piece and next piece
  - Score, level, and lines cleared
  - Block arrays (GameBlocks, BackgroundBlocks, PreviewBlocks)

### Game Modes
1. **Normal Mode** - Standard Tetris gameplay
2. **Speed Mode** - 4x faster level progression (multiplier 0.2 instead of 0.8)
3. **Timed Mode** - Time-limited rounds with winner announcement
   - Configurable duration (e.g., `/tet mode 2 120` for 120-second rounds)
   - All active games end when timer expires
   - Mode persists between rounds

## Sansar Platform Constraints

### C# 5 Compatibility Requirements
The Sansar platform uses C# 5, requiring these patterns:

```csharp
// ❌ Don't use - C# 6+ features not supported
var message = $"Position: {x},{y}";
var dict = new Dictionary<string, int> { ["key"] = value };
public string Property { get; } = "value";

// ✅ Use - C# 5 compatible patterns  
var message = string.Format("Position: {0},{1}", x, y);
var dict = new Dictionary<string, int> { {"key", value} };
private readonly string _property = "value";
public string Property { get { return _property; } }
```

### Sansar API Patterns
**Event Data Classes:**
```csharp
public interface ISimpleData
{
    AgentInfo AgentInfo { get; }
    ObjectId ObjectId { get; }
    ObjectId SourceObjectId { get; }
    Reflective ExtraData { get; }
}

public class GameEventData : Reflective, ISimpleData
{
    private readonly Reflective _extraData;
    public GameEventData(ScriptBase script) { _extraData = script; }
    // ... property implementations
}
```

**Timing System:**
```csharp
// ❌ DateTime.UtcNow not available
// ✅ Use Stopwatch for timing
private long lastDropTime = Stopwatch.GetTimestamp();
double elapsedSeconds = (double)(Stopwatch.GetTimestamp() - lastDropTime) / Stopwatch.Frequency;
```

**Coroutine Structure:**
```csharp
private void GameLoop()
{
    while (true)
    {
        // Game logic here
        Wait(TimeSpan.FromSeconds(0.1)); // Must use TimeSpan, not float
    }
}
```

## Material Control System

### Block Appearance Control
Each of the 400 blocks (200 game + 200 background) supports dynamic material control:

```csharp
// In TetrisGame.cs - Direct visibility and material control
public void SetBlockVisible(int gridX, int gridY, int pieceType, bool isActivePiece = false)
{
    MeshComponent mesh = blockMeshes[gridX, gridY];
    if (mesh != null && mesh.IsScriptable)
    {
        // First make the block visible
        mesh.SetIsVisible(true);
        
        // Then set its color and properties
        foreach (var material in mesh.GetRenderMaterials())
        {
            var props = material.GetProperties();
            props.Tint = PieceColors[pieceType];
            props.EmissiveIntensity = isActivePiece ? 2.0f : 0.0f;
            material.SetProperties(props);
        }
    }
}

// Background blocks use similar control but with fixed black color
public void SetBackgroundBlockVisible(int gridX, int gridY)
{
    MeshComponent mesh = backgroundBlockMeshes[gridX, gridY];
    mesh.SetIsVisible(true);
    // Set to BackgroundColor (0.1f, 0.1f, 0.1f, 1.0f)
}
```

**Piece Colors:**
- I-piece: Cyan
- O-piece: Yellow  
- T-piece: Purple
- S-piece: Green
- Z-piece: Red
- J-piece: Blue
- L-piece: Orange

### Flash Effects for Line Clearing
```csharp
private void SetFlashVisual(int gridX, int gridY)
{
    MeshComponent mesh = blockMeshes[gridX, gridY];
    if (mesh != null && mesh.IsScriptable)
    {
        foreach (var material in mesh.GetRenderMaterials())
        {
            var props = material.GetProperties();
            props.Tint = Sansar.Color.White;
            props.EmissiveIntensity = 15.0f;  // High intensity for flash effect
            material.SetProperties(props);
        }
    }
}
```

### Background Grid System
The background grid provides visual reference:
- Uses separate `BackgroundBlock` ClusterResource with multi-material support
- Base material: Black (non-emissive)
- Highlight1 material: Dark gray edges (0.2, 0.2, 0.2) with slight emissive (0.1)
- Same grid positions but offset on Y axis (forward/back)
- Creates subtle grid lines to help players see boundaries

## Development Knowledge Base

### Sansar Examples and Documentation
The `Sansar-Scripting-Knowledge-Base/` directory contains:
- **Assemblies/**: Sansar.Script.dll and Sansar.Simulation.dll for compilation
- **Examples1-4/**: Working Sansar script examples for reference
- **HtmlDocumentation/**: Complete Sansar API documentation
- **Guides**: Material control and object spawning documentation

### Common Sansar Patterns
Reference these examples for standard patterns:
- **Event messaging**: `Examples3/SimpleSenderScript.cs`, `SimpleListenerScript.cs`
- **Coroutines**: `Examples2/CoroutineExample.cs`
- **Material control**: `Examples4/DarkfyreAlgoma/material-api/`
- **Object spawning**: `Examples4/binah/intro-to-quests/`

## Known Issues and Limitations

### Current Implementation
- **Multi-board support**: Dynamic spawning of multiple independent game boards
- **Queue-based spawning**: Prevents throttle exceptions when creating blocks
- **SRS rotation**: Modified I-piece kicks to prevent large jumps
- **Game modes**: Normal, Speed, and Timed modes with persistent settings
- **Chat commands**: Global and board-specific command system
- **Welcome hints**: Automatic UI hints for new players joining
- **Background blocks**: Multi-material system with edge highlighting

### Platform Upload Process
1. Compile locally to verify syntax
2. Upload .cs files directly to Sansar (not .dll files)
3. Sansar compiles scripts server-side with additional restrictions
4. Test in Sansar environment for final validation

## Testing and Validation

### Local Testing

#### Known Limitations of Local Compilation
- **Vector arithmetic operations**: Cannot be verified locally due to missing Mono.Simd assembly
- **Local compilation purpose**: Syntax checking and basic API validation only
- **Vector operations work in Sansar**: Upload to Sansar platform for full Vector functionality testing
- **Color and Component types**: May show errors locally but work correctly in Sansar

```bash
# Build the unified Tetris game script
"/mnt/c/Windows/Microsoft.NET/Framework64/v4.0.30319/csc.exe" /target:library /reference:"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Script.dll" /reference:"Sansar-Scripting-Knowledge-Base/Assemblies/Sansar.Simulation.dll" /out:"bin/TetrisGame.dll" TetrisGame.cs
```

### Sansar Testing Setup
Requires minimal scene configuration:
1. **GenericBlock ClusterResource**: Game block with scriptable materials supporting Tint and Emissive
2. **BackgroundBlock ClusterResource**: Background block with Base and Highlight1 materials
3. **ChairResource ClusterResource**: Chair/seat model for spawning player positions
4. **TetrisGame.cs**: Attach to any object with proper configuration:
   - GenericBlock = Game block resource
   - BackgroundBlock = Background block resource with multi-material
   - ChairResource = Chair model for player seats
   - ChairOrigin = Position for first chair
   - GridOrigin = Bottom-left corner of first game grid
   - DefaultBoardCount = Number of boards to spawn initially

The complete setup process is documented in README.md under "In-Game Setup Instructions."

## Chat Commands

### Global Commands (Anyone can use)
- `/tet help` - Show all available commands
- `/tet help mode` - Show game mode information
- `/tet add` - Add a new game board
- `/tet score` - Show all active game scores
- `/tet mode <number> [duration]` - Set game mode:
  - `0` - Normal mode
  - `1` - Speed mode (4x faster progression)
  - `2 <seconds>` - Timed mode (e.g., `/tet mode 2 120` for 2-minute rounds)
- `/tet leaderboard` or `/tet leader` - Show top 10 all-time scores

### Board-Specific Commands (For seated players)
- `/tet start` - Start a new game
- `/tet pause` - Pause/unpause your game
- `/tet music` - Toggle background music
- `/tet volume <0-100>` - Set music volume
- `/tet track <1-6>` - Select music track

## Historical: Previous Multi-Script Architecture

*Note: This section documents the original 5-script architecture for historical reference. The current implementation uses a single unified script.*

The original design used separate scripts with event-based communication:
1. **SeatController.cs** - Player detection and game initiation
2. **GameManager.cs** - Game logic, piece movement, and player input handling  
3. **GridManager.cs** - 200-block grid spawning and state management
4. **BlockController.cs** - Individual block material control (tint, emissivity, visibility)
5. **PieceLogic.cs** - Static piece calculations and collision detection

This architecture was replaced due to:
- Complex event timing issues between scripts
- Difficulty debugging inter-script communication
- Performance overhead of event messaging
- Sansar's limitations on script-to-script data passing

The unified single-script approach eliminates these issues while maintaining the same game functionality.